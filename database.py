"""
وحدة قاعدة البيانات لتطبيق إدارة المصاريف
Database module for expense management application
"""

import sqlite3
import os
from datetime import datetime
from typing import List, Tuple, Optional

class ExpenseDatabase:
    def __init__(self, db_path: str = "expenses.db"):
        """
        تهيئة قاعدة البيانات
        Initialize the database
        """
        self.db_path = db_path
        self.init_database()

    def init_database(self):
        """
        إنشاء جداول قاعدة البيانات
        Create database tables
        """
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        # جدول المصاريف مع دعم العملات
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS expenses (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                amount REAL NOT NULL,
                category TEXT NOT NULL,
                date TEXT NOT NULL,
                notes TEXT,
                currency TEXT DEFAULT 'SAR',
                created_at TEXT DEFAULT CURRENT_TIMESTAMP
            )
        ''')

        # إضافة عمود العملة للجداول الموجودة (إذا لم يكن موجود)
        try:
            cursor.execute('ALTER TABLE expenses ADD COLUMN currency TEXT DEFAULT "SAR"')
        except:
            pass  # العمود موجود مسبقاً

        # جدول التصنيفات
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS categories (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT UNIQUE NOT NULL,
                color TEXT DEFAULT '#3498db'
            )
        ''')

        # إدراج تصنيفات افتراضية
        default_categories = [
            ('طعام وشراب', '#e74c3c'),
            ('مواصلات', '#f39c12'),
            ('ترفيه', '#9b59b6'),
            ('صحة', '#2ecc71'),
            ('تسوق', '#34495e'),
            ('فواتير', '#e67e22'),
            ('تعليم', '#3498db'),
            ('أخرى', '#95a5a6')
        ]

        cursor.executemany('''
            INSERT OR IGNORE INTO categories (name, color) VALUES (?, ?)
        ''', default_categories)

        # جدول العملات
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS currencies (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                code TEXT UNIQUE NOT NULL,
                name TEXT NOT NULL,
                symbol TEXT NOT NULL,
                exchange_rate REAL DEFAULT 1.0
            )
        ''')

        # إدراج العملات الافتراضية
        default_currencies = [
            ('SAR', 'ريال سعودي', 'ر.س', 1.0),
            ('SYP', 'ليرة سورية', 'ل.س', 0.0004),  # تقريبي
            ('USD', 'دولار أمريكي', '$', 3.75),     # تقريبي
            ('QAR', 'ريال قطري', 'ر.ق', 1.03)      # تقريبي
        ]

        cursor.executemany('''
            INSERT OR IGNORE INTO currencies (code, name, symbol, exchange_rate) VALUES (?, ?, ?, ?)
        ''', default_currencies)

        conn.commit()
        conn.close()

    def add_expense(self, amount: float, category: str, date: str, notes: str = "", currency: str = "SAR") -> bool:
        """
        إضافة مصروف جديد مع دعم العملة
        Add new expense with currency support
        """
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            cursor.execute('''
                INSERT INTO expenses (amount, category, date, notes, currency)
                VALUES (?, ?, ?, ?, ?)
            ''', (amount, category, date, notes, currency))

            conn.commit()
            conn.close()
            return True
        except Exception as e:
            print(f"خطأ في إضافة المصروف: {e}")
            return False

    def get_all_expenses(self) -> List[Tuple]:
        """
        جلب جميع المصاريف مع العملة
        Get all expenses with currency
        """
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        cursor.execute('''
            SELECT id, amount, category, date, notes, currency, created_at
            FROM expenses
            ORDER BY date DESC
        ''')

        expenses = cursor.fetchall()
        conn.close()
        return expenses

    def update_expense(self, expense_id: int, amount: float, category: str, date: str, notes: str = "", currency: str = "SAR") -> bool:
        """
        تحديث مصروف موجود مع دعم العملة
        Update existing expense with currency support
        """
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            cursor.execute('''
                UPDATE expenses
                SET amount = ?, category = ?, date = ?, notes = ?, currency = ?
                WHERE id = ?
            ''', (amount, category, date, notes, currency, expense_id))

            conn.commit()
            conn.close()
            return True
        except Exception as e:
            print(f"خطأ في تحديث المصروف: {e}")
            return False

    def delete_expense(self, expense_id: int) -> bool:
        """
        حذف مصروف
        Delete expense
        """
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            cursor.execute('DELETE FROM expenses WHERE id = ?', (expense_id,))

            conn.commit()
            conn.close()
            return True
        except Exception as e:
            print(f"خطأ في حذف المصروف: {e}")
            return False

    def get_categories(self) -> List[str]:
        """
        جلب جميع التصنيفات
        Get all categories
        """
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        cursor.execute('SELECT name FROM categories ORDER BY name')
        categories = [row[0] for row in cursor.fetchall()]

        conn.close()
        return categories

    def add_category(self, name: str, color: str = '#3498db') -> bool:
        """
        إضافة تصنيف جديد
        Add new category
        """
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # التحقق من وجود التصنيف مسبقاً
            cursor.execute('SELECT COUNT(*) FROM categories WHERE name = ?', (name,))
            if cursor.fetchone()[0] > 0:
                conn.close()
                print(f"التصنيف '{name}' موجود مسبقاً")
                return False

            # إضافة التصنيف الجديد
            cursor.execute('INSERT INTO categories (name, color) VALUES (?, ?)', (name, color))

            conn.commit()
            conn.close()
            print(f"تم إضافة التصنيف '{name}' بنجاح")
            return True
        except Exception as e:
            print(f"خطأ في إضافة التصنيف: {e}")
            return False

    def get_expenses_by_date_range(self, start_date: str, end_date: str) -> List[Tuple]:
        """
        جلب المصاريف في فترة زمنية محددة
        Get expenses within date range
        """
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        cursor.execute('''
            SELECT id, amount, category, date, notes, created_at
            FROM expenses
            WHERE date BETWEEN ? AND ?
            ORDER BY date DESC
        ''', (start_date, end_date))

        expenses = cursor.fetchall()
        conn.close()
        return expenses

    def get_total_by_category(self, start_date: str = None, end_date: str = None) -> List[Tuple]:
        """
        جلب المجموع حسب التصنيف
        Get total by category
        """
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        if start_date and end_date:
            cursor.execute('''
                SELECT category, SUM(amount) as total
                FROM expenses
                WHERE date BETWEEN ? AND ?
                GROUP BY category
                ORDER BY total DESC
            ''', (start_date, end_date))
        else:
            cursor.execute('''
                SELECT category, SUM(amount) as total
                FROM expenses
                GROUP BY category
                ORDER BY total DESC
            ''')

        totals = cursor.fetchall()
        conn.close()
        return totals

    def get_currencies(self) -> List[Tuple]:
        """
        جلب جميع العملات
        Get all currencies
        """
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        cursor.execute('SELECT code, name, symbol, exchange_rate FROM currencies ORDER BY name')
        currencies = cursor.fetchall()

        conn.close()
        return currencies

    def get_currency_symbol(self, currency_code: str) -> str:
        """
        جلب رمز العملة
        Get currency symbol
        """
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        cursor.execute('SELECT symbol FROM currencies WHERE code = ?', (currency_code,))
        result = cursor.fetchone()

        conn.close()
        return result[0] if result else currency_code

    def get_currency_name(self, currency_code: str) -> str:
        """
        جلب اسم العملة
        Get currency name
        """
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        cursor.execute('SELECT name FROM currencies WHERE code = ?', (currency_code,))
        result = cursor.fetchone()

        conn.close()
        return result[0] if result else currency_code

    def convert_currency(self, amount: float, from_currency: str, to_currency: str = "SAR") -> float:
        """
        تحويل العملة
        Convert currency
        """
        if from_currency == to_currency:
            return amount

        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        # جلب أسعار الصرف
        cursor.execute('SELECT exchange_rate FROM currencies WHERE code = ?', (from_currency,))
        from_rate = cursor.fetchone()

        cursor.execute('SELECT exchange_rate FROM currencies WHERE code = ?', (to_currency,))
        to_rate = cursor.fetchone()

        conn.close()

        if from_rate and to_rate:
            # تحويل إلى الريال السعودي أولاً ثم إلى العملة المطلوبة
            sar_amount = amount * from_rate[0]
            converted_amount = sar_amount / to_rate[0]
            return round(converted_amount, 2)

        return amount

    # ==================== دوال إدارة التصنيفات ====================

    def add_category(self, name: str, color: str = "#3498db") -> bool:
        """
        إضافة تصنيف جديد
        Add new category
        """
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            cursor.execute('INSERT INTO categories (name, color) VALUES (?, ?)', (name, color))

            conn.commit()
            conn.close()
            return True
        except Exception as e:
            print(f"خطأ في إضافة التصنيف: {e}")
            return False

    def update_category(self, category_id: int, name: str, color: str) -> bool:
        """
        تحديث تصنيف موجود
        Update existing category
        """
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            cursor.execute('UPDATE categories SET name = ?, color = ? WHERE id = ?', (name, color, category_id))

            conn.commit()
            conn.close()
            return True
        except Exception as e:
            print(f"خطأ في تحديث التصنيف: {e}")
            return False

    def delete_category(self, category_id: int) -> bool:
        """
        حذف تصنيف (مع تحويل المصاريف المرتبطة إلى 'أخرى')
        Delete category (convert related expenses to 'Other')
        """
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # الحصول على اسم التصنيف المراد حذفه
            cursor.execute('SELECT name FROM categories WHERE id = ?', (category_id,))
            result = cursor.fetchone()
            if not result:
                return False

            category_name = result[0]

            # تحويل المصاريف المرتبطة إلى 'أخرى'
            cursor.execute('UPDATE expenses SET category = ? WHERE category = ?', ('أخرى', category_name))

            # حذف التصنيف
            cursor.execute('DELETE FROM categories WHERE id = ?', (category_id,))

            conn.commit()
            conn.close()
            return True
        except Exception as e:
            print(f"خطأ في حذف التصنيف: {e}")
            return False

    def get_categories_with_count(self):
        """
        جلب التصنيفات مع عدد المصاريف لكل تصنيف
        Get categories with expense count for each
        """
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        cursor.execute('''
            SELECT c.id, c.name, c.color, COUNT(e.id) as expense_count
            FROM categories c
            LEFT JOIN expenses e ON c.name = e.category
            GROUP BY c.id, c.name, c.color
            ORDER BY c.name
        ''')

        categories = cursor.fetchall()
        conn.close()
        return categories

    def get_category_expense_count(self, category_id: int) -> int:
        """
        الحصول على عدد المصاريف لتصنيف معين
        Get expense count for specific category
        """
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        # الحصول على اسم التصنيف
        cursor.execute('SELECT name FROM categories WHERE id = ?', (category_id,))
        result = cursor.fetchone()
        if not result:
            return 0

        category_name = result[0]

        # عد المصاريف
        cursor.execute('SELECT COUNT(*) FROM expenses WHERE category = ?', (category_name,))
        count = cursor.fetchone()[0]

        conn.close()
        return count

    def get_top_categories_by_usage(self, limit: int = 5):
        """
        الحصول على أكثر التصنيفات استخداماً
        Get top categories by usage
        """
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        cursor.execute('''
            SELECT category, COUNT(*) as count,
                   (SELECT COUNT(*) FROM expenses) as total
            FROM expenses
            GROUP BY category
            ORDER BY count DESC
            LIMIT ?
        ''', (limit,))

        categories = cursor.fetchall()
        conn.close()
        return categories

    # ==================== دوال إدارة العملات ====================

    def add_currency(self, code: str, name: str, symbol: str, exchange_rate: float) -> bool:
        """
        إضافة عملة جديدة
        Add new currency
        """
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            cursor.execute('''
                INSERT INTO currencies (code, name, symbol, exchange_rate)
                VALUES (?, ?, ?, ?)
            ''', (code, name, symbol, exchange_rate))

            conn.commit()
            conn.close()
            return True
        except Exception as e:
            print(f"خطأ في إضافة العملة: {e}")
            return False

    def update_currency(self, currency_id: int, code: str, name: str, symbol: str, exchange_rate: float) -> bool:
        """
        تحديث عملة موجودة
        Update existing currency
        """
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            cursor.execute('''
                UPDATE currencies
                SET code = ?, name = ?, symbol = ?, exchange_rate = ?
                WHERE id = ?
            ''', (code, name, symbol, exchange_rate, currency_id))

            conn.commit()
            conn.close()
            return True
        except Exception as e:
            print(f"خطأ في تحديث العملة: {e}")
            return False

    def delete_currency(self, currency_id: int) -> bool:
        """
        حذف عملة (مع تحويل المصاريف المرتبطة إلى الريال السعودي)
        Delete currency (convert related expenses to SAR)
        """
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # الحصول على كود العملة المراد حذفها
            cursor.execute('SELECT code FROM currencies WHERE id = ?', (currency_id,))
            result = cursor.fetchone()
            if not result:
                return False

            currency_code = result[0]

            # تحويل المصاريف المرتبطة إلى الريال السعودي
            cursor.execute('UPDATE expenses SET currency = ? WHERE currency = ?', ('SAR', currency_code))

            # حذف العملة
            cursor.execute('DELETE FROM currencies WHERE id = ?', (currency_id,))

            conn.commit()
            conn.close()
            return True
        except Exception as e:
            print(f"خطأ في حذف العملة: {e}")
            return False

    def get_currencies_with_count(self):
        """
        جلب العملات مع عدد المصاريف لكل عملة
        Get currencies with expense count for each
        """
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        cursor.execute('''
            SELECT c.id, c.code, c.name, c.symbol, c.exchange_rate, COUNT(e.id) as expense_count
            FROM currencies c
            LEFT JOIN expenses e ON c.code = e.currency
            GROUP BY c.id, c.code, c.name, c.symbol, c.exchange_rate
            ORDER BY c.name
        ''')

        currencies = cursor.fetchall()
        conn.close()
        return currencies

    def get_currency_expense_count(self, currency_id: int) -> int:
        """
        الحصول على عدد المصاريف لعملة معينة
        Get expense count for specific currency
        """
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        # الحصول على كود العملة
        cursor.execute('SELECT code FROM currencies WHERE id = ?', (currency_id,))
        result = cursor.fetchone()
        if not result:
            return 0

        currency_code = result[0]

        # عد المصاريف
        cursor.execute('SELECT COUNT(*) FROM expenses WHERE currency = ?', (currency_code,))
        count = cursor.fetchone()[0]

        conn.close()
        return count

    def get_top_currencies_by_usage(self, limit: int = 5):
        """
        الحصول على أكثر العملات استخداماً
        Get top currencies by usage
        """
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        cursor.execute('''
            SELECT c.name, c.symbol, COUNT(e.id) as count, SUM(e.amount) as total_amount
            FROM expenses e
            JOIN currencies c ON e.currency = c.code
            GROUP BY e.currency, c.name, c.symbol
            ORDER BY count DESC
            LIMIT ?
        ''', (limit,))

        currencies = cursor.fetchall()
        conn.close()
        return currencies