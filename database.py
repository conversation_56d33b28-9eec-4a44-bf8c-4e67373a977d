"""
وحدة قاعدة البيانات لتطبيق إدارة المصاريف
Database module for expense management application
"""

import sqlite3
import os
from datetime import datetime
from typing import List, Tuple, Optional

class ExpenseDatabase:
    def __init__(self, db_path: str = "expenses.db"):
        """
        تهيئة قاعدة البيانات
        Initialize the database
        """
        self.db_path = db_path
        self.init_database()

    def init_database(self):
        """
        إنشاء جداول قاعدة البيانات
        Create database tables
        """
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        # جدول المصاريف
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS expenses (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                amount REAL NOT NULL,
                category TEXT NOT NULL,
                date TEXT NOT NULL,
                notes TEXT,
                created_at TEXT DEFAULT CURRENT_TIMESTAMP
            )
        ''')

        # جدول التصنيفات
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS categories (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT UNIQUE NOT NULL,
                color TEXT DEFAULT '#3498db'
            )
        ''')

        # إدراج تصنيفات افتراضية
        default_categories = [
            ('طعام وشراب', '#e74c3c'),
            ('مواصلات', '#f39c12'),
            ('ترفيه', '#9b59b6'),
            ('صحة', '#2ecc71'),
            ('تسوق', '#34495e'),
            ('فواتير', '#e67e22'),
            ('تعليم', '#3498db'),
            ('أخرى', '#95a5a6')
        ]

        cursor.executemany('''
            INSERT OR IGNORE INTO categories (name, color) VALUES (?, ?)
        ''', default_categories)

        conn.commit()
        conn.close()

    def add_expense(self, amount: float, category: str, date: str, notes: str = "") -> bool:
        """
        إضافة مصروف جديد
        Add new expense
        """
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            cursor.execute('''
                INSERT INTO expenses (amount, category, date, notes)
                VALUES (?, ?, ?, ?)
            ''', (amount, category, date, notes))

            conn.commit()
            conn.close()
            return True
        except Exception as e:
            print(f"خطأ في إضافة المصروف: {e}")
            return False

    def get_all_expenses(self) -> List[Tuple]:
        """
        جلب جميع المصاريف
        Get all expenses
        """
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        cursor.execute('''
            SELECT id, amount, category, date, notes, created_at
            FROM expenses
            ORDER BY date DESC
        ''')

        expenses = cursor.fetchall()
        conn.close()
        return expenses

    def update_expense(self, expense_id: int, amount: float, category: str, date: str, notes: str = "") -> bool:
        """
        تحديث مصروف موجود
        Update existing expense
        """
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            cursor.execute('''
                UPDATE expenses
                SET amount = ?, category = ?, date = ?, notes = ?
                WHERE id = ?
            ''', (amount, category, date, notes, expense_id))

            conn.commit()
            conn.close()
            return True
        except Exception as e:
            print(f"خطأ في تحديث المصروف: {e}")
            return False

    def delete_expense(self, expense_id: int) -> bool:
        """
        حذف مصروف
        Delete expense
        """
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            cursor.execute('DELETE FROM expenses WHERE id = ?', (expense_id,))

            conn.commit()
            conn.close()
            return True
        except Exception as e:
            print(f"خطأ في حذف المصروف: {e}")
            return False

    def get_categories(self) -> List[str]:
        """
        جلب جميع التصنيفات
        Get all categories
        """
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        cursor.execute('SELECT name FROM categories ORDER BY name')
        categories = [row[0] for row in cursor.fetchall()]

        conn.close()
        return categories

    def add_category(self, name: str, color: str = '#3498db') -> bool:
        """
        إضافة تصنيف جديد
        Add new category
        """
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # التحقق من وجود التصنيف مسبقاً
            cursor.execute('SELECT COUNT(*) FROM categories WHERE name = ?', (name,))
            if cursor.fetchone()[0] > 0:
                conn.close()
                print(f"التصنيف '{name}' موجود مسبقاً")
                return False

            # إضافة التصنيف الجديد
            cursor.execute('INSERT INTO categories (name, color) VALUES (?, ?)', (name, color))

            conn.commit()
            conn.close()
            print(f"تم إضافة التصنيف '{name}' بنجاح")
            return True
        except Exception as e:
            print(f"خطأ في إضافة التصنيف: {e}")
            return False

    def get_expenses_by_date_range(self, start_date: str, end_date: str) -> List[Tuple]:
        """
        جلب المصاريف في فترة زمنية محددة
        Get expenses within date range
        """
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        cursor.execute('''
            SELECT id, amount, category, date, notes, created_at
            FROM expenses
            WHERE date BETWEEN ? AND ?
            ORDER BY date DESC
        ''', (start_date, end_date))

        expenses = cursor.fetchall()
        conn.close()
        return expenses

    def get_total_by_category(self, start_date: str = None, end_date: str = None) -> List[Tuple]:
        """
        جلب المجموع حسب التصنيف
        Get total by category
        """
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        if start_date and end_date:
            cursor.execute('''
                SELECT category, SUM(amount) as total
                FROM expenses
                WHERE date BETWEEN ? AND ?
                GROUP BY category
                ORDER BY total DESC
            ''', (start_date, end_date))
        else:
            cursor.execute('''
                SELECT category, SUM(amount) as total
                FROM expenses
                GROUP BY category
                ORDER BY total DESC
            ''')

        totals = cursor.fetchall()
        conn.close()
        return totals
