# دليل حل المشاكل - Troubleshooting Guide

## 🔧 المشاكل الشائعة وحلولها

### 1. مشكلة إضافة التصنيف الجديد

**المشكلة**: عند الضغط على زر "إضافة تصنيف" لا يحدث شيء أو لا تظهر نافذة الإضافة.

**الحل**: تم إصلاح هذه المشكلة في الإصدار الحالي. إذا واجهت هذه المشكلة:

1. **أغلق التطبيق تماماً**
2. **أعد تشغيل التطبيق** باستخدام:
   ```bash
   python start_app.py
   ```
3. **جرب إضافة تصنيف جديد**:
   - انتقل إلى تبويب "إضافة مصروف"
   - اضغط على زر "إضافة تصنيف"
   - ستظهر نافذة صغيرة
   - أدخل اسم التصنيف الجديد
   - اضغط "إضافة" أو Enter

**ملاحظات**:
- ✅ النافذة ستنتظر حتى تكمل إدخال التصنيف
- ✅ ستظهر رسالة تأكيد عند النجاح
- ✅ سيتم تحديث قائمة التصنيفات تلقائياً
- ❌ لن يتم إضافة تصنيف مكرر

### 2. مشكلة عدم ظهور التقويم

**المشكلة**: لا يظهر التقويم لاختيار التاريخ.

**الحل**:
1. تأكد من تثبيت مكتبة tkcalendar:
   ```bash
   pip install tkcalendar
   ```
2. إذا لم تعمل، سيتم استخدام حقل نص عادي
3. أدخل التاريخ بصيغة: YYYY-MM-DD (مثل: 2024-01-15)

### 3. مشكلة الرسوم البيانية

**المشكلة**: لا تظهر الرسوم البيانية في تبويب الإحصائيات.

**الحل**:
1. تأكد من تثبيت matplotlib:
   ```bash
   pip install matplotlib
   ```
2. أعد تشغيل التطبيق
3. تأكد من وجود بيانات مصاريف لعرضها

### 4. مشكلة النصوص العربية

**المشكلة**: النصوص العربية تظهر بشكل غير صحيح.

**الحل**:
1. تأكد من أن نظام التشغيل يدعم الخطوط العربية
2. قد تحتاج إلى تثبيت خطوط عربية إضافية
3. في Windows، تأكد من تفعيل دعم اللغات الإضافية

### 5. مشكلة قاعدة البيانات

**المشكلة**: خطأ في الوصول إلى قاعدة البيانات.

**الحل**:
1. تأكد من وجود صلاحيات الكتابة في مجلد التطبيق
2. إذا كان الملف `expenses.db` تالف، احذفه وسيتم إنشاء ملف جديد
3. استخدم النسخ الاحتياطية إذا كانت متوفرة

### 6. مشكلة تصدير CSV

**المشكلة**: فشل في تصدير البيانات إلى CSV.

**الحل**:
1. تأكد من وجود صلاحيات الكتابة في المجلد المختار
2. تأكد من عدم فتح ملف CSV في برنامج آخر
3. جرب حفظ الملف في مجلد مختلف

## 🚀 نصائح للاستخدام الأمثل

### إضافة التصنيفات بشكل صحيح:
1. اضغط على زر "إضافة تصنيف" في تبويب "إضافة مصروف"
2. انتظر ظهور النافذة الصغيرة
3. أدخل اسم التصنيف بوضوح
4. اضغط "إضافة" أو مفتاح Enter
5. ستظهر رسالة تأكيد
6. سيتم تحديث القائمة تلقائياً

### نسخ احتياطية منتظمة:
- اضغط على زر "نسخة احتياطية" بانتظام
- احتفظ بالنسخ الاحتياطية في مكان آمن
- الملفات تُحفظ بصيغة: `backup_expenses_YYYYMMDD_HHMMSS.db`

### تنظيم المصاريف:
- استخدم تصنيفات واضحة ومحددة
- أضف ملاحظات مفيدة لكل مصروف
- راجع الإحصائيات بانتظام

## 📞 الحصول على المساعدة

إذا واجهت مشكلة لم تُذكر هنا:

1. **تحقق من رسائل الخطأ** في وحدة التحكم
2. **أعد تشغيل التطبيق** - يحل معظم المشاكل
3. **تأكد من تثبيت جميع المتطلبات**:
   ```bash
   pip install -r requirements.txt
   ```
4. **جرب الملف المحسن للتشغيل**:
   ```bash
   python start_app.py
   ```

## ✅ اختبار سريع

لاختبار أن كل شيء يعمل:

1. **تشغيل التطبيق**: `python start_app.py`
2. **إضافة تصنيف جديد**: تبويب "إضافة مصروف" → "إضافة تصنيف"
3. **إضافة مصروف**: املأ النموذج واضغط "حفظ"
4. **عرض المصاريف**: تبويب "عرض المصاريف"
5. **شاهد الإحصائيات**: تبويب "الإحصائيات"
6. **تصدير البيانات**: زر "تصدير إلى CSV"

إذا نجحت جميع الخطوات، فالتطبيق يعمل بشكل مثالي! 🎉
