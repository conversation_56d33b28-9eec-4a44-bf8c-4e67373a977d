"""
جدول عرض وإدارة المصاريف
Expense display and management table
"""

import tkinter as tk
from tkinter import ttk, messagebox
from datetime import datetime

class ExpenseTable:
    def __init__(self, parent, database, fonts, colors, callback=None):
        """
        تهيئة جدول المصاريف المحسن
        Initialize enhanced expense table
        """
        self.parent = parent
        self.db = database
        self.fonts = fonts
        self.colors = colors
        self.callback = callback

        # للتوافق مع الكود القديم
        self.font = fonts['default']

        self.create_table()
        self.refresh()

    def create_table(self):
        """
        إنشاء جدول المصاريف المحسن
        Create enhanced expense table
        """
        # تعيين خلفية الإطار الرئيسي (إذا كان Frame عادي)
        try:
            self.parent.configure(bg=self.colors['background'])
        except:
            pass  # في حالة كان ttk.Frame

        # الإطار الرئيسي
        main_frame = tk.Frame(self.parent, bg=self.colors['background'])
        main_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)

        # العنوان وأدوات التحكم
        header_frame = tk.Frame(main_frame, bg=self.colors['background'])
        header_frame.pack(fill=tk.X, pady=(0, 20))

        # العنوان مع أيقونة
        title_label = tk.Label(
            header_frame,
            text="📊 جدول المصاريف",
            font=self.fonts['large'],
            fg=self.colors['primary'],
            bg=self.colors['background']
        )
        title_label.pack(side=tk.RIGHT)

        # العنوان الفرعي
        subtitle_label = tk.Label(
            header_frame,
            text="عرض وإدارة جميع المصاريف المسجلة",
            font=self.fonts['default'],
            fg=self.colors['dark'],
            bg=self.colors['background']
        )
        subtitle_label.pack(side=tk.RIGHT, padx=(0, 20))

        # أزرار التحكم المحسنة
        control_frame = tk.Frame(header_frame, bg=self.colors['background'])
        control_frame.pack(side=tk.LEFT)

        # زر التحديث
        refresh_btn = tk.Button(
            control_frame,
            text="🔄 تحديث",
            command=self.refresh,
            font=self.fonts['default'],
            bg=self.colors['info'],
            fg=self.colors['white'],
            relief='flat',
            padx=15,
            pady=8,
            cursor='hand2'
        )
        refresh_btn.pack(side=tk.LEFT, padx=(0, 10))

        # زر التعديل
        edit_btn = tk.Button(
            control_frame,
            text="✏️ تعديل",
            command=self.edit_expense,
            font=self.fonts['default'],
            bg=self.colors['warning'],
            fg=self.colors['white'],
            relief='flat',
            padx=15,
            pady=8,
            cursor='hand2'
        )
        edit_btn.pack(side=tk.LEFT, padx=(0, 10))

        # زر الحذف
        delete_btn = tk.Button(
            control_frame,
            text="🗑️ حذف",
            command=self.delete_expense,
            font=self.fonts['default'],
            bg=self.colors['danger'],
            fg=self.colors['white'],
            relief='flat',
            padx=15,
            pady=8,
            cursor='hand2'
        )
        delete_btn.pack(side=tk.LEFT)

        # إطار البحث والفلترة
        filter_frame = ttk.LabelFrame(main_frame, text="البحث والفلترة", padding=10)
        filter_frame.pack(fill=tk.X, pady=(0, 10))

        # البحث بالتصنيف
        search_frame = ttk.Frame(filter_frame)
        search_frame.pack(fill=tk.X, pady=(0, 5))

        tk.Label(search_frame, text="التصنيف:", font=self.font).pack(side=tk.RIGHT, padx=(10, 0))
        self.filter_category_var = tk.StringVar()
        self.filter_category_combo = ttk.Combobox(
            search_frame,
            textvariable=self.filter_category_var,
            font=self.font,
            width=15
        )
        self.filter_category_combo.pack(side=tk.RIGHT, padx=(0, 10))

        # فلترة بالتاريخ
        date_frame = ttk.Frame(filter_frame)
        date_frame.pack(fill=tk.X, pady=(0, 5))

        tk.Label(date_frame, text="من تاريخ:", font=self.font).pack(side=tk.RIGHT, padx=(10, 0))
        self.start_date_var = tk.StringVar()
        start_date_entry = ttk.Entry(date_frame, textvariable=self.start_date_var, width=12)
        start_date_entry.pack(side=tk.RIGHT, padx=(0, 10))

        tk.Label(date_frame, text="إلى تاريخ:", font=self.font).pack(side=tk.RIGHT, padx=(10, 0))
        self.end_date_var = tk.StringVar()
        end_date_entry = ttk.Entry(date_frame, textvariable=self.end_date_var, width=12)
        end_date_entry.pack(side=tk.RIGHT, padx=(0, 10))

        # أزرار الفلترة
        filter_btn_frame = ttk.Frame(filter_frame)
        filter_btn_frame.pack(fill=tk.X)

        ttk.Button(filter_btn_frame, text="تطبيق الفلتر", command=self.apply_filter).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(filter_btn_frame, text="إزالة الفلتر", command=self.clear_filter).pack(side=tk.LEFT)

        # إطار الجدول
        table_frame = ttk.Frame(main_frame)
        table_frame.pack(fill=tk.BOTH, expand=True)

        # إنشاء الجدول
        columns = ('id', 'amount', 'category', 'date', 'notes', 'created_at')
        self.tree = ttk.Treeview(table_frame, columns=columns, show='headings', height=15)

        # تعريف العناوين
        self.tree.heading('id', text='الرقم')
        self.tree.heading('amount', text='المبلغ')
        self.tree.heading('category', text='التصنيف')
        self.tree.heading('date', text='التاريخ')
        self.tree.heading('notes', text='الملاحظات')
        self.tree.heading('created_at', text='تاريخ الإنشاء')

        # تعريف عرض الأعمدة
        self.tree.column('id', width=60, anchor=tk.CENTER)
        self.tree.column('amount', width=100, anchor=tk.CENTER)
        self.tree.column('category', width=120, anchor=tk.CENTER)
        self.tree.column('date', width=100, anchor=tk.CENTER)
        self.tree.column('notes', width=200, anchor=tk.E)
        self.tree.column('created_at', width=150, anchor=tk.CENTER)

        # شريط التمرير
        scrollbar = ttk.Scrollbar(table_frame, orient=tk.VERTICAL, command=self.tree.yview)
        self.tree.configure(yscrollcommand=scrollbar.set)

        # تخطيط الجدول
        self.tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # ربط الأحداث
        self.tree.bind('<Double-1>', lambda e: self.edit_expense())
        self.tree.bind('<Delete>', lambda e: self.delete_expense())

        # إطار الإحصائيات السريعة
        stats_frame = ttk.LabelFrame(main_frame, text="إحصائيات سريعة", padding=10)
        stats_frame.pack(fill=tk.X, pady=(10, 0))

        self.stats_label = tk.Label(
            stats_frame,
            text="",
            font=self.font,
            fg="#2c3e50"
        )
        self.stats_label.pack()

        # تحميل التصنيفات للفلتر
        self.load_filter_categories()

    def load_filter_categories(self):
        """
        تحميل التصنيفات لفلتر البحث
        Load categories for search filter
        """
        try:
            categories = ['جميع التصنيفات'] + self.db.get_categories()
            self.filter_category_combo['values'] = categories
            self.filter_category_combo.set('جميع التصنيفات')
        except Exception as e:
            print(f"خطأ في تحميل التصنيفات: {e}")

    def refresh(self):
        """
        تحديث بيانات الجدول
        Refresh table data
        """
        try:
            # مسح البيانات الحالية
            for item in self.tree.get_children():
                self.tree.delete(item)

            # جلب البيانات الجديدة
            expenses = self.db.get_all_expenses()

            total_amount = 0
            for expense in expenses:
                # إدراج البيانات في الجدول
                self.tree.insert('', tk.END, values=expense)
                total_amount += expense[1]  # المبلغ في العمود الثاني

            # تحديث الإحصائيات
            self.update_stats(len(expenses), total_amount)

        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ أثناء تحديث البيانات: {str(e)}")

    def apply_filter(self):
        """
        تطبيق الفلتر على البيانات
        Apply filter to data
        """
        try:
            # مسح البيانات الحالية
            for item in self.tree.get_children():
                self.tree.delete(item)

            # الحصول على معايير الفلتر
            category_filter = self.filter_category_var.get()
            start_date = self.start_date_var.get()
            end_date = self.end_date_var.get()

            # جلب البيانات
            if start_date and end_date:
                expenses = self.db.get_expenses_by_date_range(start_date, end_date)
            else:
                expenses = self.db.get_all_expenses()

            # تطبيق فلتر التصنيف
            if category_filter and category_filter != 'جميع التصنيفات':
                expenses = [exp for exp in expenses if exp[2] == category_filter]

            total_amount = 0
            for expense in expenses:
                self.tree.insert('', tk.END, values=expense)
                total_amount += expense[1]

            # تحديث الإحصائيات
            self.update_stats(len(expenses), total_amount)

        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ أثناء تطبيق الفلتر: {str(e)}")

    def clear_filter(self):
        """
        إزالة الفلتر وعرض جميع البيانات
        Clear filter and show all data
        """
        self.filter_category_var.set('جميع التصنيفات')
        self.start_date_var.set('')
        self.end_date_var.set('')
        self.refresh()

    def update_stats(self, count, total):
        """
        تحديث الإحصائيات السريعة
        Update quick statistics
        """
        stats_text = f"عدد المصاريف: {count} | المجموع الكلي: {total:.2f} ريال"
        self.stats_label.config(text=stats_text)

    def get_selected_expense(self):
        """
        الحصول على المصروف المحدد
        Get selected expense
        """
        selection = self.tree.selection()
        if not selection:
            messagebox.showwarning("تحذير", "يرجى اختيار مصروف من الجدول")
            return None

        item = self.tree.item(selection[0])
        return item['values']

    def edit_expense(self):
        """
        تعديل المصروف المحدد
        Edit selected expense
        """
        expense_data = self.get_selected_expense()
        if not expense_data:
            return

        # فتح نافذة التعديل
        EditExpenseDialog(self.parent, self.db, self.font, expense_data, self.on_expense_updated)

    def delete_expense(self):
        """
        حذف المصروف المحدد
        Delete selected expense
        """
        expense_data = self.get_selected_expense()
        if not expense_data:
            return

        # تأكيد الحذف
        result = messagebox.askyesno(
            "تأكيد الحذف",
            f"هل أنت متأكد من حذف هذا المصروف؟\nالمبلغ: {expense_data[1]} ريال\nالتصنيف: {expense_data[2]}"
        )

        if result:
            success = self.db.delete_expense(expense_data[0])
            if success:
                self.refresh()
                if self.callback:
                    self.callback()
                messagebox.showinfo("نجح", "تم حذف المصروف بنجاح")
            else:
                messagebox.showerror("خطأ", "فشل في حذف المصروف")

    def on_expense_updated(self):
        """
        معالج تحديث المصروف
        Expense update handler
        """
        self.refresh()
        if self.callback:
            self.callback()


class EditExpenseDialog:
    def __init__(self, parent, database, font, expense_data, callback=None):
        """
        نافذة تعديل المصروف
        Edit expense dialog
        """
        self.db = database
        self.font = font
        self.expense_data = expense_data
        self.callback = callback

        # إنشاء النافذة
        self.dialog = tk.Toplevel(parent)
        self.dialog.title("تعديل المصروف")
        self.dialog.geometry("400x300")
        self.dialog.resizable(False, False)
        self.dialog.grab_set()
        self.dialog.transient(parent)

        self.create_dialog()
        self.load_data()

    def create_dialog(self):
        """
        إنشاء محتوى النافذة
        Create dialog content
        """
        main_frame = ttk.Frame(self.dialog, padding=20)
        main_frame.pack(fill=tk.BOTH, expand=True)

        # المبلغ
        amount_frame = ttk.Frame(main_frame)
        amount_frame.pack(fill=tk.X, pady=(0, 10))
        tk.Label(amount_frame, text="المبلغ:", font=self.font).pack(side=tk.RIGHT, padx=(10, 0))
        self.amount_var = tk.StringVar()
        ttk.Entry(amount_frame, textvariable=self.amount_var, font=self.font, width=20).pack(side=tk.RIGHT)

        # التصنيف
        category_frame = ttk.Frame(main_frame)
        category_frame.pack(fill=tk.X, pady=(0, 10))
        tk.Label(category_frame, text="التصنيف:", font=self.font).pack(side=tk.RIGHT, padx=(10, 0))
        self.category_var = tk.StringVar()
        self.category_combo = ttk.Combobox(category_frame, textvariable=self.category_var, font=self.font, width=18)
        self.category_combo.pack(side=tk.RIGHT)

        # التاريخ
        date_frame = ttk.Frame(main_frame)
        date_frame.pack(fill=tk.X, pady=(0, 10))
        tk.Label(date_frame, text="التاريخ:", font=self.font).pack(side=tk.RIGHT, padx=(10, 0))
        self.date_var = tk.StringVar()
        ttk.Entry(date_frame, textvariable=self.date_var, font=self.font, width=20).pack(side=tk.RIGHT)

        # الملاحظات
        notes_frame = ttk.Frame(main_frame)
        notes_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 15))
        tk.Label(notes_frame, text="الملاحظات:", font=self.font).pack(anchor=tk.E, pady=(0, 5))
        self.notes_text = tk.Text(notes_frame, height=4, font=self.font, wrap=tk.WORD)
        self.notes_text.pack(fill=tk.BOTH, expand=True)

        # أزرار التحكم
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill=tk.X)
        ttk.Button(button_frame, text="حفظ", command=self.save_changes).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(button_frame, text="إلغاء", command=self.dialog.destroy).pack(side=tk.LEFT)

        # تحميل التصنيفات
        categories = self.db.get_categories()
        self.category_combo['values'] = categories

    def load_data(self):
        """
        تحميل بيانات المصروف للتعديل
        Load expense data for editing
        """
        self.amount_var.set(str(self.expense_data[1]))
        self.category_var.set(self.expense_data[2])
        self.date_var.set(self.expense_data[3])
        self.notes_text.insert("1.0", self.expense_data[4] or "")

    def save_changes(self):
        """
        حفظ التغييرات
        Save changes
        """
        try:
            amount = float(self.amount_var.get())
            category = self.category_var.get()
            date = self.date_var.get()
            notes = self.notes_text.get("1.0", tk.END).strip()

            success = self.db.update_expense(
                self.expense_data[0],  # ID
                amount, category, date, notes
            )

            if success:
                if self.callback:
                    self.callback()
                messagebox.showinfo("نجح", "تم تحديث المصروف بنجاح")
                self.dialog.destroy()
            else:
                messagebox.showerror("خطأ", "فشل في تحديث المصروف")

        except ValueError:
            messagebox.showerror("خطأ", "يرجى إدخال مبلغ صحيح")
        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ: {str(e)}")
