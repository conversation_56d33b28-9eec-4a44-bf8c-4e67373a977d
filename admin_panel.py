"""
لوحة التحكم الإدارية - Admin Panel
إدارة التصنيفات والعملات
"""

import tkinter as tk
from tkinter import ttk, messagebox, colorchooser
from typing import Dict, Any, Callable, Optional
import sqlite3


class AdminPanel:
    def __init__(self, parent, database, fonts: Dict[str, Any], colors: Dict[str, str], callback: Optional[Callable] = None):
        """
        تهيئة لوحة التحكم الإدارية
        Initialize admin panel
        """
        self.parent = parent
        self.db = database
        self.fonts = fonts
        self.colors = colors
        self.callback = callback

        # متغيرات التصنيفات
        self.selected_category_id = None
        self.category_name_var = tk.StringVar()
        self.category_color_var = tk.StringVar()

        # متغيرات العملات
        self.selected_currency_id = None
        self.currency_code_var = tk.StringVar()
        self.currency_name_var = tk.StringVar()
        self.currency_symbol_var = tk.StringVar()
        self.currency_rate_var = tk.StringVar()

        self.create_widgets()
        self.load_data()

    def create_widgets(self):
        """إنشاء عناصر الواجهة"""
        # تطبيق لون الخلفية
        try:
            self.parent.configure(bg=self.colors['background'])
        except:
            pass

        # العنوان الرئيسي
        title_frame = tk.Frame(self.parent, bg=self.colors['background'])
        title_frame.pack(fill=tk.X, pady=(0, 20))

        title_label = tk.Label(
            title_frame,
            text="⚙️ لوحة التحكم الإدارية",
            font=self.fonts['large'],
            fg=self.colors['primary'],
            bg=self.colors['background']
        )
        title_label.pack()

        subtitle_label = tk.Label(
            title_frame,
            text="إدارة التصنيفات والعملات",
            font=self.fonts['default'],
            fg=self.colors['dark'],
            bg=self.colors['background']
        )
        subtitle_label.pack(pady=(5, 0))

        # إنشاء Notebook للتبويبات
        self.notebook = ttk.Notebook(self.parent)
        self.notebook.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # تبويب إدارة التصنيفات
        self.categories_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.categories_frame, text="📂 إدارة التصنيفات")
        self.create_categories_tab()

        # تبويب إدارة العملات
        self.currencies_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.currencies_frame, text="💱 إدارة العملات")
        self.create_currencies_tab()

        # تبويب الإحصائيات
        self.stats_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.stats_frame, text="📊 الإحصائيات")
        self.create_stats_tab()

    def create_categories_tab(self):
        """إنشاء تبويب إدارة التصنيفات"""
        main_frame = tk.Frame(self.categories_frame, bg=self.colors['background'])
        main_frame.pack(fill=tk.BOTH, expand=True, padx=15, pady=15)

        # قسم إضافة/تعديل التصنيف
        form_frame = tk.LabelFrame(
            main_frame,
            text="إضافة/تعديل تصنيف",
            font=self.fonts['bold'],
            bg=self.colors['card'],
            fg=self.colors['primary']
        )
        form_frame.pack(fill=tk.X, pady=(0, 15))

        # اسم التصنيف
        name_frame = tk.Frame(form_frame, bg=self.colors['card'])
        name_frame.pack(fill=tk.X, padx=15, pady=10)

        tk.Label(
            name_frame,
            text="📝 اسم التصنيف:",
            font=self.fonts['default'],
            bg=self.colors['card'],
            fg=self.colors['dark']
        ).pack(side=tk.RIGHT, padx=(10, 0))

        tk.Entry(
            name_frame,
            textvariable=self.category_name_var,
            font=self.fonts['default'],
            width=25
        ).pack(side=tk.RIGHT)

        # لون التصنيف
        color_frame = tk.Frame(form_frame, bg=self.colors['card'])
        color_frame.pack(fill=tk.X, padx=15, pady=(0, 10))

        tk.Label(
            color_frame,
            text="🎨 لون التصنيف:",
            font=self.fonts['default'],
            bg=self.colors['card'],
            fg=self.colors['dark']
        ).pack(side=tk.RIGHT, padx=(10, 0))

        self.color_preview = tk.Label(
            color_frame,
            text="     ",
            bg="#3498db",
            relief='solid',
            bd=1
        )
        self.color_preview.pack(side=tk.RIGHT, padx=(0, 10))

        tk.Button(
            color_frame,
            text="اختيار لون",
            command=self.choose_color,
            font=self.fonts['small'],
            bg=self.colors['secondary'],
            fg=self.colors['white'],
            relief='flat',
            padx=15
        ).pack(side=tk.RIGHT)

        # أزرار التحكم
        buttons_frame = tk.Frame(form_frame, bg=self.colors['card'])
        buttons_frame.pack(fill=tk.X, padx=15, pady=(0, 15))

        tk.Button(
            buttons_frame,
            text="➕ إضافة تصنيف",
            command=self.add_category,
            font=self.fonts['default'],
            bg=self.colors['success'],
            fg=self.colors['white'],
            relief='flat',
            padx=20,
            pady=8
        ).pack(side=tk.LEFT, padx=(0, 10))

        tk.Button(
            buttons_frame,
            text="✏️ تحديث",
            command=self.update_category,
            font=self.fonts['default'],
            bg=self.colors['warning'],
            fg=self.colors['white'],
            relief='flat',
            padx=20,
            pady=8
        ).pack(side=tk.LEFT, padx=(0, 10))

        tk.Button(
            buttons_frame,
            text="🗑️ حذف",
            command=self.delete_category,
            font=self.fonts['default'],
            bg=self.colors['danger'],
            fg=self.colors['white'],
            relief='flat',
            padx=20,
            pady=8
        ).pack(side=tk.LEFT, padx=(0, 10))

        tk.Button(
            buttons_frame,
            text="🔄 مسح",
            command=self.clear_category_form,
            font=self.fonts['default'],
            bg=self.colors['info'],
            fg=self.colors['white'],
            relief='flat',
            padx=20,
            pady=8
        ).pack(side=tk.LEFT)

        # قائمة التصنيفات
        list_frame = tk.LabelFrame(
            main_frame,
            text="التصنيفات الموجودة",
            font=self.fonts['bold'],
            bg=self.colors['card'],
            fg=self.colors['primary']
        )
        list_frame.pack(fill=tk.BOTH, expand=True)

        # جدول التصنيفات
        columns = ('id', 'name', 'color', 'count')
        self.categories_tree = ttk.Treeview(list_frame, columns=columns, show='headings', height=10)

        self.categories_tree.heading('id', text='الرقم')
        self.categories_tree.heading('name', text='اسم التصنيف')
        self.categories_tree.heading('color', text='اللون')
        self.categories_tree.heading('count', text='عدد المصاريف')

        self.categories_tree.column('id', width=60, anchor=tk.CENTER)
        self.categories_tree.column('name', width=150, anchor=tk.CENTER)
        self.categories_tree.column('color', width=100, anchor=tk.CENTER)
        self.categories_tree.column('count', width=100, anchor=tk.CENTER)

        # شريط التمرير للتصنيفات
        categories_scrollbar = ttk.Scrollbar(list_frame, orient=tk.VERTICAL, command=self.categories_tree.yview)
        self.categories_tree.configure(yscrollcommand=categories_scrollbar.set)

        self.categories_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=15, pady=15)
        categories_scrollbar.pack(side=tk.RIGHT, fill=tk.Y, pady=15)

        # ربط حدث النقر
        self.categories_tree.bind('<ButtonRelease-1>', self.on_category_select)

    def create_currencies_tab(self):
        """إنشاء تبويب إدارة العملات"""
        main_frame = tk.Frame(self.currencies_frame, bg=self.colors['background'])
        main_frame.pack(fill=tk.BOTH, expand=True, padx=15, pady=15)

        # قسم إضافة/تعديل العملة
        form_frame = tk.LabelFrame(
            main_frame,
            text="إضافة/تعديل عملة",
            font=self.fonts['bold'],
            bg=self.colors['card'],
            fg=self.colors['primary']
        )
        form_frame.pack(fill=tk.X, pady=(0, 15))

        # كود العملة
        code_frame = tk.Frame(form_frame, bg=self.colors['card'])
        code_frame.pack(fill=tk.X, padx=15, pady=10)

        tk.Label(
            code_frame,
            text="🔤 كود العملة:",
            font=self.fonts['default'],
            bg=self.colors['card'],
            fg=self.colors['dark']
        ).pack(side=tk.RIGHT, padx=(10, 0))

        tk.Entry(
            code_frame,
            textvariable=self.currency_code_var,
            font=self.fonts['default'],
            width=10
        ).pack(side=tk.RIGHT)

        # اسم العملة
        name_frame = tk.Frame(form_frame, bg=self.colors['card'])
        name_frame.pack(fill=tk.X, padx=15, pady=(0, 10))

        tk.Label(
            name_frame,
            text="📝 اسم العملة:",
            font=self.fonts['default'],
            bg=self.colors['card'],
            fg=self.colors['dark']
        ).pack(side=tk.RIGHT, padx=(10, 0))

        tk.Entry(
            name_frame,
            textvariable=self.currency_name_var,
            font=self.fonts['default'],
            width=25
        ).pack(side=tk.RIGHT)

        # رمز العملة
        symbol_frame = tk.Frame(form_frame, bg=self.colors['card'])
        symbol_frame.pack(fill=tk.X, padx=15, pady=(0, 10))

        tk.Label(
            symbol_frame,
            text="💰 رمز العملة:",
            font=self.fonts['default'],
            bg=self.colors['card'],
            fg=self.colors['dark']
        ).pack(side=tk.RIGHT, padx=(10, 0))

        tk.Entry(
            symbol_frame,
            textvariable=self.currency_symbol_var,
            font=self.fonts['default'],
            width=10
        ).pack(side=tk.RIGHT)

        # سعر الصرف
        rate_frame = tk.Frame(form_frame, bg=self.colors['card'])
        rate_frame.pack(fill=tk.X, padx=15, pady=(0, 10))

        tk.Label(
            rate_frame,
            text="📈 سعر الصرف (مقابل الريال السعودي):",
            font=self.fonts['default'],
            bg=self.colors['card'],
            fg=self.colors['dark']
        ).pack(side=tk.RIGHT, padx=(10, 0))

        tk.Entry(
            rate_frame,
            textvariable=self.currency_rate_var,
            font=self.fonts['default'],
            width=15
        ).pack(side=tk.RIGHT)

        # أزرار التحكم
        buttons_frame = tk.Frame(form_frame, bg=self.colors['card'])
        buttons_frame.pack(fill=tk.X, padx=15, pady=(0, 15))

        tk.Button(
            buttons_frame,
            text="➕ إضافة عملة",
            command=self.add_currency,
            font=self.fonts['default'],
            bg=self.colors['success'],
            fg=self.colors['white'],
            relief='flat',
            padx=20,
            pady=8
        ).pack(side=tk.LEFT, padx=(0, 10))

        tk.Button(
            buttons_frame,
            text="✏️ تحديث",
            command=self.update_currency,
            font=self.fonts['default'],
            bg=self.colors['warning'],
            fg=self.colors['white'],
            relief='flat',
            padx=20,
            pady=8
        ).pack(side=tk.LEFT, padx=(0, 10))

        tk.Button(
            buttons_frame,
            text="🗑️ حذف",
            command=self.delete_currency,
            font=self.fonts['default'],
            bg=self.colors['danger'],
            fg=self.colors['white'],
            relief='flat',
            padx=20,
            pady=8
        ).pack(side=tk.LEFT, padx=(0, 10))

        tk.Button(
            buttons_frame,
            text="🔄 مسح",
            command=self.clear_currency_form,
            font=self.fonts['default'],
            bg=self.colors['info'],
            fg=self.colors['white'],
            relief='flat',
            padx=20,
            pady=8
        ).pack(side=tk.LEFT)

        # قائمة العملات
        list_frame = tk.LabelFrame(
            main_frame,
            text="العملات الموجودة",
            font=self.fonts['bold'],
            bg=self.colors['card'],
            fg=self.colors['primary']
        )
        list_frame.pack(fill=tk.BOTH, expand=True)

        # جدول العملات
        columns = ('id', 'code', 'name', 'symbol', 'rate', 'count')
        self.currencies_tree = ttk.Treeview(list_frame, columns=columns, show='headings', height=10)

        self.currencies_tree.heading('id', text='الرقم')
        self.currencies_tree.heading('code', text='الكود')
        self.currencies_tree.heading('name', text='اسم العملة')
        self.currencies_tree.heading('symbol', text='الرمز')
        self.currencies_tree.heading('rate', text='سعر الصرف')
        self.currencies_tree.heading('count', text='عدد المصاريف')

        self.currencies_tree.column('id', width=50, anchor=tk.CENTER)
        self.currencies_tree.column('code', width=80, anchor=tk.CENTER)
        self.currencies_tree.column('name', width=150, anchor=tk.CENTER)
        self.currencies_tree.column('symbol', width=80, anchor=tk.CENTER)
        self.currencies_tree.column('rate', width=100, anchor=tk.CENTER)
        self.currencies_tree.column('count', width=100, anchor=tk.CENTER)

        # شريط التمرير للعملات
        currencies_scrollbar = ttk.Scrollbar(list_frame, orient=tk.VERTICAL, command=self.currencies_tree.yview)
        self.currencies_tree.configure(yscrollcommand=currencies_scrollbar.set)

        self.currencies_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=15, pady=15)
        currencies_scrollbar.pack(side=tk.RIGHT, fill=tk.Y, pady=15)

        # ربط حدث النقر
        self.currencies_tree.bind('<ButtonRelease-1>', self.on_currency_select)

    def create_stats_tab(self):
        """إنشاء تبويب الإحصائيات"""
        main_frame = tk.Frame(self.stats_frame, bg=self.colors['background'])
        main_frame.pack(fill=tk.BOTH, expand=True, padx=15, pady=15)

        # إحصائيات التصنيفات
        categories_stats_frame = tk.LabelFrame(
            main_frame,
            text="📊 إحصائيات التصنيفات",
            font=self.fonts['bold'],
            bg=self.colors['card'],
            fg=self.colors['primary']
        )
        categories_stats_frame.pack(fill=tk.X, pady=(0, 15))

        self.categories_stats_text = tk.Text(
            categories_stats_frame,
            height=8,
            font=self.fonts['default'],
            bg=self.colors['white'],
            fg=self.colors['dark'],
            wrap=tk.WORD,
            state=tk.DISABLED
        )
        self.categories_stats_text.pack(fill=tk.X, padx=15, pady=15)

        # إحصائيات العملات
        currencies_stats_frame = tk.LabelFrame(
            main_frame,
            text="💱 إحصائيات العملات",
            font=self.fonts['bold'],
            bg=self.colors['card'],
            fg=self.colors['primary']
        )
        currencies_stats_frame.pack(fill=tk.X, pady=(0, 15))

        self.currencies_stats_text = tk.Text(
            currencies_stats_frame,
            height=8,
            font=self.fonts['default'],
            bg=self.colors['white'],
            fg=self.colors['dark'],
            wrap=tk.WORD,
            state=tk.DISABLED
        )
        self.currencies_stats_text.pack(fill=tk.X, padx=15, pady=15)

        # أزرار تحديث الإحصائيات
        stats_buttons_frame = tk.Frame(main_frame, bg=self.colors['background'])
        stats_buttons_frame.pack(fill=tk.X)

        tk.Button(
            stats_buttons_frame,
            text="🔄 تحديث الإحصائيات",
            command=self.update_stats,
            font=self.fonts['bold'],
            bg=self.colors['primary'],
            fg=self.colors['white'],
            relief='flat',
            padx=30,
            pady=10
        ).pack()

    # ==================== دوال إدارة التصنيفات ====================

    def choose_color(self):
        """اختيار لون للتصنيف"""
        color = colorchooser.askcolor(title="اختيار لون التصنيف")
        if color[1]:  # إذا تم اختيار لون
            self.category_color_var.set(color[1])
            self.color_preview.config(bg=color[1])

    def add_category(self):
        """إضافة تصنيف جديد"""
        name = self.category_name_var.get().strip()
        color = self.category_color_var.get() or "#3498db"

        if not name:
            messagebox.showerror("خطأ", "يرجى إدخال اسم التصنيف")
            return

        try:
            success = self.db.add_category(name, color)
            if success:
                messagebox.showinfo("نجح", "تم إضافة التصنيف بنجاح")
                self.clear_category_form()
                self.load_categories()
                if self.callback:
                    self.callback()
            else:
                messagebox.showerror("خطأ", "فشل في إضافة التصنيف")
        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ: {str(e)}")

    def update_category(self):
        """تحديث تصنيف موجود"""
        if not self.selected_category_id:
            messagebox.showerror("خطأ", "يرجى اختيار تصنيف للتحديث")
            return

        name = self.category_name_var.get().strip()
        color = self.category_color_var.get() or "#3498db"

        if not name:
            messagebox.showerror("خطأ", "يرجى إدخال اسم التصنيف")
            return

        try:
            success = self.db.update_category(self.selected_category_id, name, color)
            if success:
                messagebox.showinfo("نجح", "تم تحديث التصنيف بنجاح")
                self.clear_category_form()
                self.load_categories()
                if self.callback:
                    self.callback()
            else:
                messagebox.showerror("خطأ", "فشل في تحديث التصنيف")
        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ: {str(e)}")

    def delete_category(self):
        """حذف تصنيف"""
        if not self.selected_category_id:
            messagebox.showerror("خطأ", "يرجى اختيار تصنيف للحذف")
            return

        # التحقق من وجود مصاريف مرتبطة
        count = self.db.get_category_expense_count(self.selected_category_id)
        if count > 0:
            result = messagebox.askyesno(
                "تأكيد الحذف",
                f"هذا التصنيف يحتوي على {count} مصروف.\n"
                "هل تريد حذفه؟ (سيتم تحويل المصاريف إلى 'أخرى')"
            )
            if not result:
                return

        try:
            success = self.db.delete_category(self.selected_category_id)
            if success:
                messagebox.showinfo("نجح", "تم حذف التصنيف بنجاح")
                self.clear_category_form()
                self.load_categories()
                if self.callback:
                    self.callback()
            else:
                messagebox.showerror("خطأ", "فشل في حذف التصنيف")
        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ: {str(e)}")

    def clear_category_form(self):
        """مسح نموذج التصنيف"""
        self.selected_category_id = None
        self.category_name_var.set("")
        self.category_color_var.set("#3498db")
        self.color_preview.config(bg="#3498db")

    def on_category_select(self, event):
        """معالج اختيار تصنيف من الجدول"""
        selection = self.categories_tree.selection()
        if selection:
            item = self.categories_tree.item(selection[0])
            values = item['values']

            self.selected_category_id = values[0]
            self.category_name_var.set(values[1])
            self.category_color_var.set(values[2])
            self.color_preview.config(bg=values[2])

    # ==================== دوال إدارة العملات ====================

    def add_currency(self):
        """إضافة عملة جديدة"""
        code = self.currency_code_var.get().strip().upper()
        name = self.currency_name_var.get().strip()
        symbol = self.currency_symbol_var.get().strip()
        rate_str = self.currency_rate_var.get().strip()

        if not all([code, name, symbol, rate_str]):
            messagebox.showerror("خطأ", "يرجى ملء جميع الحقول")
            return

        try:
            rate = float(rate_str)
            if rate <= 0:
                messagebox.showerror("خطأ", "سعر الصرف يجب أن يكون أكبر من صفر")
                return

            success = self.db.add_currency(code, name, symbol, rate)
            if success:
                messagebox.showinfo("نجح", "تم إضافة العملة بنجاح")
                self.clear_currency_form()
                self.load_currencies()
                if self.callback:
                    self.callback()
            else:
                messagebox.showerror("خطأ", "فشل في إضافة العملة (ربما الكود موجود مسبقاً)")
        except ValueError:
            messagebox.showerror("خطأ", "يرجى إدخال سعر صرف صحيح")
        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ: {str(e)}")

    def update_currency(self):
        """تحديث عملة موجودة"""
        if not self.selected_currency_id:
            messagebox.showerror("خطأ", "يرجى اختيار عملة للتحديث")
            return

        code = self.currency_code_var.get().strip().upper()
        name = self.currency_name_var.get().strip()
        symbol = self.currency_symbol_var.get().strip()
        rate_str = self.currency_rate_var.get().strip()

        if not all([code, name, symbol, rate_str]):
            messagebox.showerror("خطأ", "يرجى ملء جميع الحقول")
            return

        try:
            rate = float(rate_str)
            if rate <= 0:
                messagebox.showerror("خطأ", "سعر الصرف يجب أن يكون أكبر من صفر")
                return

            success = self.db.update_currency(self.selected_currency_id, code, name, symbol, rate)
            if success:
                messagebox.showinfo("نجح", "تم تحديث العملة بنجاح")
                self.clear_currency_form()
                self.load_currencies()
                if self.callback:
                    self.callback()
            else:
                messagebox.showerror("خطأ", "فشل في تحديث العملة")
        except ValueError:
            messagebox.showerror("خطأ", "يرجى إدخال سعر صرف صحيح")
        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ: {str(e)}")

    def delete_currency(self):
        """حذف عملة"""
        if not self.selected_currency_id:
            messagebox.showerror("خطأ", "يرجى اختيار عملة للحذف")
            return

        # التحقق من وجود مصاريف مرتبطة
        count = self.db.get_currency_expense_count(self.selected_currency_id)
        if count > 0:
            result = messagebox.askyesno(
                "تأكيد الحذف",
                f"هذه العملة تحتوي على {count} مصروف.\n"
                "هل تريد حذفها؟ (سيتم تحويل المصاريف إلى الريال السعودي)"
            )
            if not result:
                return

        try:
            success = self.db.delete_currency(self.selected_currency_id)
            if success:
                messagebox.showinfo("نجح", "تم حذف العملة بنجاح")
                self.clear_currency_form()
                self.load_currencies()
                if self.callback:
                    self.callback()
            else:
                messagebox.showerror("خطأ", "فشل في حذف العملة")
        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ: {str(e)}")

    def clear_currency_form(self):
        """مسح نموذج العملة"""
        self.selected_currency_id = None
        self.currency_code_var.set("")
        self.currency_name_var.set("")
        self.currency_symbol_var.set("")
        self.currency_rate_var.set("")

    def on_currency_select(self, event):
        """معالج اختيار عملة من الجدول"""
        selection = self.currencies_tree.selection()
        if selection:
            item = self.currencies_tree.item(selection[0])
            values = item['values']

            self.selected_currency_id = values[0]
            self.currency_code_var.set(values[1])
            self.currency_name_var.set(values[2])
            self.currency_symbol_var.set(values[3])
            self.currency_rate_var.set(values[4])

    # ==================== دوال التحميل والإحصائيات ====================

    def load_data(self):
        """تحميل جميع البيانات"""
        self.load_categories()
        self.load_currencies()
        self.update_stats()

    def load_categories(self):
        """تحميل التصنيفات"""
        try:
            # مسح الجدول
            for item in self.categories_tree.get_children():
                self.categories_tree.delete(item)

            # جلب التصنيفات مع عدد المصاريف
            categories = self.db.get_categories_with_count()

            for category_id, name, color, count in categories:
                self.categories_tree.insert('', tk.END, values=(category_id, name, color, count))

        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ في تحميل التصنيفات: {str(e)}")

    def load_currencies(self):
        """تحميل العملات"""
        try:
            # مسح الجدول
            for item in self.currencies_tree.get_children():
                self.currencies_tree.delete(item)

            # جلب العملات مع عدد المصاريف
            currencies = self.db.get_currencies_with_count()

            for currency_id, code, name, symbol, rate, count in currencies:
                self.currencies_tree.insert('', tk.END, values=(currency_id, code, name, symbol, f"{rate:.4f}", count))

        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ في تحميل العملات: {str(e)}")

    def update_stats(self):
        """تحديث الإحصائيات"""
        try:
            # إحصائيات التصنيفات
            categories_stats = self.get_categories_stats()
            self.categories_stats_text.config(state=tk.NORMAL)
            self.categories_stats_text.delete(1.0, tk.END)
            self.categories_stats_text.insert(1.0, categories_stats)
            self.categories_stats_text.config(state=tk.DISABLED)

            # إحصائيات العملات
            currencies_stats = self.get_currencies_stats()
            self.currencies_stats_text.config(state=tk.NORMAL)
            self.currencies_stats_text.delete(1.0, tk.END)
            self.currencies_stats_text.insert(1.0, currencies_stats)
            self.currencies_stats_text.config(state=tk.DISABLED)

        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ في تحديث الإحصائيات: {str(e)}")

    def get_categories_stats(self):
        """الحصول على إحصائيات التصنيفات"""
        try:
            stats = "📊 إحصائيات التصنيفات:\n\n"

            # إجمالي التصنيفات
            total_categories = len(self.db.get_categories())
            stats += f"• إجمالي التصنيفات: {total_categories}\n\n"

            # أكثر التصنيفات استخداماً
            top_categories = self.db.get_top_categories_by_usage(5)
            stats += "🏆 أكثر التصنيفات استخداماً:\n"
            for i, (category, count, total) in enumerate(top_categories, 1):
                percentage = (count / total * 100) if total > 0 else 0
                stats += f"{i}. {category}: {count} مصروف ({percentage:.1f}%)\n"

            return stats

        except Exception as e:
            return f"خطأ في جلب إحصائيات التصنيفات: {str(e)}"

    def get_currencies_stats(self):
        """الحصول على إحصائيات العملات"""
        try:
            stats = "💱 إحصائيات العملات:\n\n"

            # إجمالي العملات
            total_currencies = len(self.db.get_currencies())
            stats += f"• إجمالي العملات: {total_currencies}\n\n"

            # أكثر العملات استخداماً
            top_currencies = self.db.get_top_currencies_by_usage(5)
            stats += "🏆 أكثر العملات استخداماً:\n"
            for i, (currency, symbol, count, total_amount) in enumerate(top_currencies, 1):
                stats += f"{i}. {currency} ({symbol}): {count} مصروف\n"
                stats += f"   إجمالي المبلغ: {total_amount:.2f} {symbol}\n"

            return stats

        except Exception as e:
            return f"خطأ في جلب إحصائيات العملات: {str(e)}"
