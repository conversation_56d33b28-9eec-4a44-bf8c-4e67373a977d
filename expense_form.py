"""
نموذج إدخال المصاريف
Expense input form
"""

import tkinter as tk
from tkinter import ttk, messagebox
from datetime import datetime
try:
    from tkcalendar import DateEntry
    CALENDAR_AVAILABLE = True
except ImportError:
    CALENDAR_AVAILABLE = False

class ExpenseForm:
    def __init__(self, parent, database, fonts, colors, callback=None):
        """
        تهيئة نموذج إدخال المصاريف
        Initialize expense input form
        """
        self.parent = parent
        self.db = database
        self.fonts = fonts
        self.colors = colors
        self.callback = callback

        # للتوافق مع الكود القديم
        self.font = fonts['default']

        self.create_form()
        self.load_categories()

    def create_form(self):
        """
        إنشاء نموذج الإدخال الاحترافي
        Create professional input form
        """
        # تعيين خلفية الإطار الرئيسي (إذا كان Frame عادي)
        try:
            self.parent.configure(bg=self.colors['background'])
        except:
            pass  # في حالة كان ttk.Frame

        # إنشاء Canvas مع Scrollbar للتخطيط الديناميكي
        self.canvas = tk.Canvas(self.parent, bg=self.colors['background'])
        scrollbar = tk.Scrollbar(self.parent, orient="vertical", command=self.canvas.yview)
        self.scrollable_frame = tk.Frame(self.canvas, bg=self.colors['background'])

        # ربط الأحداث للتخطيط الديناميكي
        self.scrollable_frame.bind("<Configure>", self._on_frame_configure)
        self.canvas.bind("<Configure>", self._on_canvas_configure)

        self.canvas_window = self.canvas.create_window((0, 0), window=self.scrollable_frame, anchor="nw")
        self.canvas.configure(yscrollcommand=scrollbar.set)

        # تخطيط Canvas و Scrollbar
        self.canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")

        # الإطار الرئيسي داخل الـ scrollable frame
        main_frame = tk.Frame(self.scrollable_frame, bg=self.colors['background'])
        main_frame.pack(fill=tk.BOTH, expand=True, padx=15, pady=15)

        # ربط عجلة الماوس بالتمرير
        self.canvas.bind_all("<MouseWheel>", self._on_mousewheel)

        # العنوان مبسط
        title_label = tk.Label(
            main_frame,
            text="💳 إضافة مصروف جديد",
            font=self.fonts['medium'],
            fg=self.colors['primary'],
            bg=self.colors['background']
        )
        title_label.pack(pady=(0, 10))

        # إطار النموذج الاحترافي
        form_frame = tk.Frame(main_frame, bg=self.colors['card'], relief='flat', bd=1)
        form_frame.pack(fill=tk.X, pady=(0, 10), padx=5)

        # إطار داخلي للحشو
        inner_frame = tk.Frame(form_frame, bg=self.colors['card'])
        inner_frame.pack(fill=tk.X, padx=15, pady=15)

        # حقل المبلغ
        self.create_amount_field(inner_frame)

        # حقل التصنيف
        self.create_category_field(inner_frame)

        # حقل التاريخ
        self.create_date_field(inner_frame)

        # حقل العملة
        self.create_currency_field(inner_frame)

        # حقل الملاحظات
        self.create_notes_field(inner_frame)

        # أزرار التحكم
        self.create_action_buttons(main_frame)

        # إعداد اختصارات لوحة المفاتيح
        self.setup_keyboard_shortcuts()

    def _on_frame_configure(self, event):
        """تحديث منطقة التمرير عند تغيير حجم الإطار"""
        self.canvas.configure(scrollregion=self.canvas.bbox("all"))

    def _on_canvas_configure(self, event):
        """تحديث عرض الإطار الداخلي عند تغيير حجم Canvas"""
        canvas_width = event.width
        self.canvas.itemconfig(self.canvas_window, width=canvas_width)

    def _on_mousewheel(self, event):
        """التمرير بعجلة الماوس"""
        self.canvas.yview_scroll(int(-1*(event.delta/120)), "units")

    def create_amount_field(self, parent):
        """إنشاء حقل المبلغ"""
        amount_frame = tk.Frame(parent, bg=self.colors['card'])
        amount_frame.pack(fill=tk.X, pady=(0, 10))

        # تسمية الحقل
        label = tk.Label(
            amount_frame,
            text="💰 المبلغ:",
            font=self.fonts['default'],
            fg=self.colors['primary'],
            bg=self.colors['card']
        )
        label.pack(anchor=tk.E, pady=(0, 5))

        # إطار الإدخال مع تخطيط ديناميكي
        input_frame = tk.Frame(amount_frame, bg=self.colors['card'])
        input_frame.pack(fill=tk.X)
        input_frame.columnconfigure(0, weight=1)  # العمود الأول يتمدد

        self.amount_var = tk.StringVar()
        amount_entry = tk.Entry(
            input_frame,
            textvariable=self.amount_var,
            font=self.fonts['default'],
            relief='flat',
            bd=1,
            highlightthickness=2,
            highlightcolor=self.colors['secondary'],
            bg=self.colors['white']
        )
        amount_entry.grid(row=0, column=0, sticky="ew", padx=(0, 10))

        # تسمية العملة (ستتحدث ديناميكياً)
        self.currency_label = tk.Label(
            input_frame,
            text="ر.س",
            font=self.fonts['default'],
            fg=self.colors['dark'],
            bg=self.colors['card']
        )
        self.currency_label.grid(row=0, column=1, sticky="e")

    def create_category_field(self, parent):
        """إنشاء حقل التصنيف"""
        category_frame = tk.Frame(parent, bg=self.colors['card'])
        category_frame.pack(fill=tk.X, pady=(0, 10))

        # تسمية الحقل
        label = tk.Label(
            category_frame,
            text="📂 التصنيف:",
            font=self.fonts['default'],
            fg=self.colors['primary'],
            bg=self.colors['card']
        )
        label.pack(anchor=tk.E, pady=(0, 5))

        # إطار الإدخال مع تخطيط ديناميكي
        input_frame = tk.Frame(category_frame, bg=self.colors['card'])
        input_frame.pack(fill=tk.X)
        input_frame.columnconfigure(0, weight=1)  # العمود الأول يتمدد

        self.category_var = tk.StringVar()
        self.category_combo = ttk.Combobox(
            input_frame,
            textvariable=self.category_var,
            font=self.fonts['default'],
            state="readonly"
        )
        self.category_combo.grid(row=0, column=0, sticky="ew", padx=(0, 10))

        # زر إضافة تصنيف جديد
        add_category_btn = tk.Button(
            input_frame,
            text="➕ إضافة",
            command=self.add_new_category,
            font=self.fonts['small'],
            bg=self.colors['success'],
            fg=self.colors['white'],
            relief='flat',
            padx=15,
            pady=5
        )
        add_category_btn.grid(row=0, column=1, sticky="e")

    def create_date_field(self, parent):
        """إنشاء حقل التاريخ"""
        date_frame = tk.Frame(parent, bg=self.colors['card'])
        date_frame.pack(fill=tk.X, pady=(0, 10))

        # تسمية الحقل
        label = tk.Label(
            date_frame,
            text="📅 التاريخ:",
            font=self.fonts['default'],
            fg=self.colors['primary'],
            bg=self.colors['card']
        )
        label.pack(anchor=tk.E, pady=(0, 5))

        # إطار الإدخال مع تخطيط ديناميكي
        input_frame = tk.Frame(date_frame, bg=self.colors['card'])
        input_frame.pack(fill=tk.X)
        input_frame.columnconfigure(0, weight=1)  # العمود الأول يتمدد

        # استخدام DateEntry إذا كان متاحاً، وإلا استخدم Entry عادي
        if CALENDAR_AVAILABLE:
            try:
                self.date_entry = DateEntry(
                    input_frame,
                    background=self.colors['secondary'],
                    foreground='white',
                    borderwidth=1,
                    date_pattern='yyyy-mm-dd',
                    font=self.fonts['default']
                )
                self.date_entry.set_date(datetime.now().date())
            except:
                # في حالة فشل DateEntry
                self.date_var = tk.StringVar(value=datetime.now().strftime('%Y-%m-%d'))
                self.date_entry = tk.Entry(
                    input_frame,
                    textvariable=self.date_var,
                    font=self.fonts['default'],
                    relief='flat',
                    bd=1,
                    bg=self.colors['white']
                )
        else:
            # في حالة عدم توفر tkcalendar
            self.date_var = tk.StringVar(value=datetime.now().strftime('%Y-%m-%d'))
            self.date_entry = tk.Entry(
                input_frame,
                textvariable=self.date_var,
                font=self.fonts['default'],
                relief='flat',
                bd=1,
                bg=self.colors['white']
            )

        self.date_entry.grid(row=0, column=0, sticky="ew", padx=(0, 10))

        # زر التاريخ الحالي
        today_btn = tk.Button(
            input_frame,
            text="📅 اليوم",
            command=self.set_today_date,
            font=self.fonts['small'],
            bg=self.colors['info'],
            fg=self.colors['white'],
            relief='flat',
            padx=15,
            pady=5
        )
        today_btn.grid(row=0, column=1, sticky="e")

    def create_currency_field(self, parent):
        """إنشاء حقل العملة"""
        currency_frame = tk.Frame(parent, bg=self.colors['card'])
        currency_frame.pack(fill=tk.X, pady=(0, 10))

        # تسمية الحقل
        label = tk.Label(
            currency_frame,
            text="💱 العملة:",
            font=self.fonts['default'],
            fg=self.colors['primary'],
            bg=self.colors['card']
        )
        label.pack(anchor=tk.E, pady=(0, 5))

        # إطار الإدخال مع تخطيط ديناميكي
        input_frame = tk.Frame(currency_frame, bg=self.colors['card'])
        input_frame.pack(fill=tk.X)
        input_frame.columnconfigure(0, weight=1)  # العمود الأول يتمدد

        self.currency_var = tk.StringVar()
        self.currency_combo = ttk.Combobox(
            input_frame,
            textvariable=self.currency_var,
            font=self.fonts['default'],
            state="readonly"
        )
        self.currency_combo.grid(row=0, column=0, sticky="ew", padx=(0, 10))

        # ربط حدث تغيير العملة
        self.currency_combo.bind('<<ComboboxSelected>>', self.on_currency_change)

        # زر تحديث أسعار الصرف
        update_rates_btn = tk.Button(
            input_frame,
            text="💱 تحديث",
            command=self.update_exchange_rates,
            font=self.fonts['small'],
            bg=self.colors['secondary'],
            fg=self.colors['white'],
            relief='flat',
            padx=15,
            pady=5
        )
        update_rates_btn.grid(row=0, column=1, sticky="e")

        # تحميل العملات
        self.load_currencies()

    def create_notes_field(self, parent):
        """إنشاء حقل الملاحظات"""
        notes_frame = tk.Frame(parent, bg=self.colors['card'])
        notes_frame.pack(fill=tk.X, pady=(0, 5))

        # تسمية الحقل
        label = tk.Label(
            notes_frame,
            text="📝 الملاحظات:",
            font=self.fonts['default'],
            fg=self.colors['primary'],
            bg=self.colors['card']
        )
        label.pack(anchor=tk.E, pady=(0, 5))

        # حقل النص
        self.notes_text = tk.Text(
            notes_frame,
            height=2,
            font=self.fonts['default'],
            wrap=tk.WORD,
            relief='flat',
            bd=1,
            bg=self.colors['white'],
            highlightthickness=1,
            highlightcolor=self.colors['secondary']
        )
        self.notes_text.pack(fill=tk.X)

    def create_action_buttons(self, parent):
        """إنشاء أزرار التحكم الديناميكية"""
        button_frame = tk.Frame(parent, bg=self.colors['background'])
        button_frame.pack(fill=tk.X, pady=(5, 0))

        # تخطيط شبكي للأزرار
        button_frame.columnconfigure(0, weight=1)
        button_frame.columnconfigure(1, weight=1)
        button_frame.columnconfigure(2, weight=1)

        # زر الحفظ
        save_btn = tk.Button(
            button_frame,
            text="💾 حفظ المصروف",
            command=self.save_expense,
            font=self.fonts['bold'],
            bg=self.colors['success'],
            fg=self.colors['white'],
            relief='flat',
            padx=20,
            pady=10,
            cursor='hand2'
        )
        save_btn.grid(row=0, column=0, sticky="ew", padx=(0, 5))

        # زر المسح
        clear_btn = tk.Button(
            button_frame,
            text="🗑️ مسح النموذج",
            command=self.clear_form,
            font=self.fonts['default'],
            bg=self.colors['warning'],
            fg=self.colors['white'],
            relief='flat',
            padx=15,
            pady=10,
            cursor='hand2'
        )
        clear_btn.grid(row=0, column=1, sticky="ew", padx=5)

        # زر إضافي للمساحة (يمكن إضافة وظائف لاحقاً)
        info_btn = tk.Button(
            button_frame,
            text="ℹ️ مساعدة",
            command=self.show_help,
            font=self.fonts['default'],
            bg=self.colors['info'],
            fg=self.colors['white'],
            relief='flat',
            padx=15,
            pady=10,
            cursor='hand2'
        )
        info_btn.grid(row=0, column=2, sticky="ew", padx=(5, 0))

    def show_help(self):
        """عرض نافذة المساعدة"""
        help_text = """
        💡 نصائح لاستخدام النموذج:

        💰 المبلغ: أدخل المبلغ بالأرقام فقط
        📂 التصنيف: اختر من القائمة أو أضف جديد
        📅 التاريخ: اضغط "اليوم" للتاريخ الحالي
        📝 الملاحظات: اختياري - أضف تفاصيل إضافية

        ⌨️ اختصارات:
        • Ctrl+S: حفظ سريع
        • Ctrl+R: مسح النموذج
        • Tab: الانتقال بين الحقول
        """

        messagebox.showinfo("مساعدة النموذج", help_text)

    def setup_keyboard_shortcuts(self):
        """إعداد اختصارات لوحة المفاتيح"""
        try:
            # ربط الاختصارات بالنافذة الرئيسية
            root = self.parent.winfo_toplevel()

            # Ctrl+S للحفظ
            root.bind('<Control-s>', lambda e: self.save_expense())
            root.bind('<Control-S>', lambda e: self.save_expense())

            # Ctrl+R لمسح النموذج
            root.bind('<Control-r>', lambda e: self.clear_form())
            root.bind('<Control-R>', lambda e: self.clear_form())

            # F1 للمساعدة
            root.bind('<F1>', lambda e: self.show_help())

            # Enter في حقل المبلغ ينتقل للتصنيف
            if hasattr(self, 'amount_var'):
                # سيتم ربطها بعد إنشاء الحقول
                pass

        except Exception as e:
            print(f"خطأ في إعداد اختصارات لوحة المفاتيح: {e}")

    def load_currencies(self):
        """
        تحميل العملات من قاعدة البيانات
        Load currencies from database
        """
        try:
            currencies = self.db.get_currencies()
            currency_options = []

            for code, name, symbol, rate in currencies:
                currency_options.append(f"{name} ({symbol})")

            self.currency_combo['values'] = currency_options
            if currency_options:
                self.currency_combo.set(currency_options[0])  # الريال السعودي افتراضياً
                self.on_currency_change()  # تحديث رمز العملة

        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ في تحميل العملات: {str(e)}")

    def on_currency_change(self, event=None):
        """
        معالج تغيير العملة
        Currency change handler
        """
        try:
            selected = self.currency_var.get()
            if selected:
                # استخراج رمز العملة من النص المختار
                if "ر.س" in selected:
                    symbol = "ر.س"
                    code = "SAR"
                elif "ل.س" in selected:
                    symbol = "ل.س"
                    code = "SYP"
                elif "$" in selected:
                    symbol = "$"
                    code = "USD"
                elif "ر.ق" in selected:
                    symbol = "ر.ق"
                    code = "QAR"
                else:
                    symbol = selected.split("(")[1].split(")")[0] if "(" in selected else ""
                    code = "SAR"

                # تحديث تسمية العملة في حقل المبلغ
                if hasattr(self, 'currency_label'):
                    self.currency_label.config(text=symbol)

        except Exception as e:
            print(f"خطأ في تغيير العملة: {e}")

    def get_selected_currency_code(self):
        """
        الحصول على كود العملة المختارة
        Get selected currency code
        """
        selected = self.currency_var.get()
        if "ريال سعودي" in selected:
            return "SAR"
        elif "ليرة سورية" in selected:
            return "SYP"
        elif "دولار أمريكي" in selected:
            return "USD"
        elif "ريال قطري" in selected:
            return "QAR"
        return "SAR"  # افتراضي

    def update_exchange_rates(self):
        """
        تحديث أسعار الصرف (يمكن ربطها بـ API لاحقاً)
        Update exchange rates (can be connected to API later)
        """
        messagebox.showinfo(
            "تحديث أسعار الصرف",
            "ميزة تحديث أسعار الصرف ستكون متاحة في الإصدارات القادمة.\n"
            "حالياً يتم استخدام أسعار تقريبية ثابتة."
        )

    def load_categories(self):
        """
        تحميل التصنيفات من قاعدة البيانات
        Load categories from database
        """
        try:
            categories = self.db.get_categories()
            self.category_combo['values'] = categories
            if categories:
                self.category_combo.set(categories[0])
        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ في تحميل التصنيفات: {str(e)}")

    def add_new_category(self):
        """
        إضافة تصنيف جديد
        Add new category
        """
        dialog = CategoryDialog(self.parent, self.db, self.fonts, self.colors)
        self.parent.wait_window(dialog.dialog)  # انتظار إغلاق النافذة
        if dialog.result:
            self.load_categories()
            self.category_combo.set(dialog.result)

    def set_today_date(self):
        """
        تعيين التاريخ الحالي
        Set today's date
        """
        try:
            if hasattr(self.date_entry, 'set_date'):
                self.date_entry.set_date(datetime.now().date())
            else:
                self.date_var.set(datetime.now().strftime('%Y-%m-%d'))
        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ في تعيين التاريخ: {str(e)}")

    def validate_input(self):
        """
        التحقق من صحة البيانات المدخلة
        Validate input data
        """
        # التحقق من المبلغ
        try:
            amount = float(self.amount_var.get())
            if amount <= 0:
                messagebox.showerror("خطأ", "يجب أن يكون المبلغ أكبر من صفر")
                return False
        except ValueError:
            messagebox.showerror("خطأ", "يرجى إدخال مبلغ صحيح")
            return False

        # التحقق من التصنيف
        if not self.category_var.get():
            messagebox.showerror("خطأ", "يرجى اختيار تصنيف")
            return False

        # التحقق من التاريخ
        try:
            if hasattr(self.date_entry, 'get_date'):
                date_str = self.date_entry.get_date().strftime('%Y-%m-%d')
            else:
                date_str = self.date_var.get()
                datetime.strptime(date_str, '%Y-%m-%d')
        except ValueError:
            messagebox.showerror("خطأ", "يرجى إدخال تاريخ صحيح (YYYY-MM-DD)")
            return False

        return True

    def save_expense(self):
        """
        حفظ المصروف
        Save expense
        """
        if not self.validate_input():
            return

        try:
            amount = float(self.amount_var.get())
            category = self.category_var.get()
            currency = self.get_selected_currency_code()

            if hasattr(self.date_entry, 'get_date'):
                date_str = self.date_entry.get_date().strftime('%Y-%m-%d')
            else:
                date_str = self.date_var.get()

            notes = self.notes_text.get("1.0", tk.END).strip()

            success = self.db.add_expense(amount, category, date_str, notes, currency)

            if success:
                self.clear_form()
                if self.callback:
                    self.callback()
            else:
                messagebox.showerror("خطأ", "فشل في حفظ المصروف")

        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ أثناء الحفظ: {str(e)}")

    def clear_form(self):
        """
        مسح النموذج
        Clear form
        """
        self.amount_var.set("")
        if self.category_combo['values']:
            self.category_combo.set(self.category_combo['values'][0])
        if self.currency_combo['values']:
            self.currency_combo.set(self.currency_combo['values'][0])
            self.on_currency_change()  # تحديث رمز العملة
        self.set_today_date()
        self.notes_text.delete("1.0", tk.END)


class CategoryDialog:
    def __init__(self, parent, database, fonts, colors=None):
        """
        نافذة حوار إضافة تصنيف جديد محسنة
        Enhanced dialog for adding new category
        """
        self.db = database
        self.fonts = fonts if isinstance(fonts, dict) else {'default': fonts}
        self.colors = colors or {
            'primary': '#2c3e50',
            'success': '#27ae60',
            'white': '#ffffff',
            'background': '#ecf0f1',
            'card': '#ffffff',
            'dark': '#343a40'
        }
        self.result = None

        # إنشاء النافذة
        self.dialog = tk.Toplevel(parent)
        self.dialog.title("➕ إضافة تصنيف جديد")
        self.dialog.geometry("400x200")
        self.dialog.resizable(False, False)
        self.dialog.grab_set()
        self.dialog.configure(bg=self.colors['background'])

        # توسيط النافذة
        self.dialog.transient(parent)

        self.create_dialog()

    def create_dialog(self):
        """
        إنشاء محتوى النافذة المحسن
        Create enhanced dialog content
        """
        main_frame = tk.Frame(self.dialog, bg=self.colors['background'])
        main_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)

        # العنوان
        title_label = tk.Label(
            main_frame,
            text="📂 إضافة تصنيف جديد",
            font=self.fonts.get('large', self.fonts['default']),
            fg=self.colors['primary'],
            bg=self.colors['background']
        )
        title_label.pack(pady=(0, 20))

        # إطار الإدخال
        input_frame = tk.Frame(main_frame, bg=self.colors['card'], relief='flat', bd=1)
        input_frame.pack(fill=tk.X, pady=(0, 20))

        inner_frame = tk.Frame(input_frame, bg=self.colors['card'])
        inner_frame.pack(fill=tk.X, padx=20, pady=20)

        # تسمية الحقل
        label = tk.Label(
            inner_frame,
            text="اسم التصنيف:",
            font=self.fonts.get('bold', self.fonts['default']),
            fg=self.colors['primary'],
            bg=self.colors['card']
        )
        label.pack(anchor=tk.E, pady=(0, 8))

        # حقل الإدخال
        self.name_var = tk.StringVar()
        name_entry = tk.Entry(
            inner_frame,
            textvariable=self.name_var,
            font=self.fonts['default'],
            relief='flat',
            bd=1,
            bg=self.colors['white'],
            highlightthickness=2,
            highlightcolor=self.colors['primary']
        )
        name_entry.pack(fill=tk.X, pady=(0, 10))
        name_entry.focus()

        # نص مساعد
        help_label = tk.Label(
            inner_frame,
            text="أدخل اسم التصنيف الجديد (مثل: مطاعم، هدايا، صيانة)",
            font=self.fonts.get('small', self.fonts['default']),
            fg=self.colors['dark'],
            bg=self.colors['card']
        )
        help_label.pack(anchor=tk.E)

        # أزرار التحكم
        button_frame = tk.Frame(main_frame, bg=self.colors['background'])
        button_frame.pack(fill=tk.X)

        # زر الإضافة
        add_btn = tk.Button(
            button_frame,
            text="✅ إضافة التصنيف",
            command=self.add_category,
            font=self.fonts.get('bold', self.fonts['default']),
            bg=self.colors['success'],
            fg=self.colors['white'],
            relief='flat',
            padx=20,
            pady=10,
            cursor='hand2'
        )
        add_btn.pack(side=tk.LEFT, padx=(0, 15))

        # زر الإلغاء
        cancel_btn = tk.Button(
            button_frame,
            text="❌ إلغاء",
            command=self.dialog.destroy,
            font=self.fonts['default'],
            bg=self.colors['dark'],
            fg=self.colors['white'],
            relief='flat',
            padx=20,
            pady=10,
            cursor='hand2'
        )
        cancel_btn.pack(side=tk.LEFT)

        # ربط مفاتيح الاختصار
        self.dialog.bind('<Return>', lambda e: self.add_category())
        self.dialog.bind('<Escape>', lambda e: self.dialog.destroy())

    def add_category(self):
        """
        إضافة التصنيف
        Add category
        """
        name = self.name_var.get().strip()

        if not name:
            messagebox.showerror("خطأ", "يرجى إدخال اسم التصنيف")
            return

        try:
            success = self.db.add_category(name)

            if success:
                self.result = name
                messagebox.showinfo("نجح", f"تم إضافة التصنيف '{name}' بنجاح!")
                self.dialog.destroy()
            else:
                messagebox.showerror("خطأ", "فشل في إضافة التصنيف أو أنه موجود مسبقاً")
        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ أثناء إضافة التصنيف: {str(e)}")
