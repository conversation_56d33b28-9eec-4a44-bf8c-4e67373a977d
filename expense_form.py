"""
نموذج إدخال المصاريف
Expense input form
"""

import tkinter as tk
from tkinter import ttk, messagebox
from datetime import datetime
try:
    from tkcalendar import DateEntry
    CALENDAR_AVAILABLE = True
except ImportError:
    CALENDAR_AVAILABLE = False

class ExpenseForm:
    def __init__(self, parent, database, font, callback=None):
        """
        تهيئة نموذج إدخال المصاريف
        Initialize expense input form
        """
        self.parent = parent
        self.db = database
        self.font = font
        self.callback = callback

        self.create_form()
        self.load_categories()

    def create_form(self):
        """
        إنشاء نموذج الإدخال
        Create input form
        """
        # الإطار الرئيسي
        main_frame = ttk.Frame(self.parent)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)

        # العنوان
        title_label = tk.Label(
            main_frame,
            text="إضافة مصروف جديد",
            font=(self.font[0], 14, "bold"),
            fg="#2c3e50"
        )
        title_label.pack(pady=(0, 30))

        # إطار النموذج
        form_frame = ttk.LabelFrame(main_frame, text="بيانات المصروف", padding=20)
        form_frame.pack(fill=tk.X, pady=(0, 20))

        # المبلغ
        amount_frame = ttk.Frame(form_frame)
        amount_frame.pack(fill=tk.X, pady=(0, 15))

        tk.Label(amount_frame, text="المبلغ:", font=self.font).pack(side=tk.RIGHT, padx=(10, 0))
        self.amount_var = tk.StringVar()
        amount_entry = ttk.Entry(
            amount_frame,
            textvariable=self.amount_var,
            font=self.font,
            width=20
        )
        amount_entry.pack(side=tk.RIGHT, padx=(0, 10))

        # العملة
        tk.Label(amount_frame, text="ريال", font=self.font).pack(side=tk.RIGHT)

        # التصنيف
        category_frame = ttk.Frame(form_frame)
        category_frame.pack(fill=tk.X, pady=(0, 15))

        tk.Label(category_frame, text="التصنيف:", font=self.font).pack(side=tk.RIGHT, padx=(10, 0))
        self.category_var = tk.StringVar()
        self.category_combo = ttk.Combobox(
            category_frame,
            textvariable=self.category_var,
            font=self.font,
            width=18,
            state="readonly"
        )
        self.category_combo.pack(side=tk.RIGHT, padx=(0, 10))

        # زر إضافة تصنيف جديد
        add_category_btn = ttk.Button(
            category_frame,
            text="إضافة تصنيف",
            command=self.add_new_category
        )
        add_category_btn.pack(side=tk.RIGHT)

        # التاريخ
        date_frame = ttk.Frame(form_frame)
        date_frame.pack(fill=tk.X, pady=(0, 15))

        tk.Label(date_frame, text="التاريخ:", font=self.font).pack(side=tk.RIGHT, padx=(10, 0))

        # استخدام DateEntry إذا كان متاحاً، وإلا استخدم Entry عادي
        if CALENDAR_AVAILABLE:
            try:
                self.date_entry = DateEntry(
                    date_frame,
                    width=18,
                    background='darkblue',
                    foreground='white',
                    borderwidth=2,
                    date_pattern='yyyy-mm-dd',
                    font=self.font
                )
                self.date_entry.set_date(datetime.now().date())
            except:
                # في حالة فشل DateEntry
                self.date_var = tk.StringVar(value=datetime.now().strftime('%Y-%m-%d'))
                self.date_entry = ttk.Entry(
                    date_frame,
                    textvariable=self.date_var,
                    font=self.font,
                    width=20
                )
        else:
            # في حالة عدم توفر tkcalendar
            self.date_var = tk.StringVar(value=datetime.now().strftime('%Y-%m-%d'))
            self.date_entry = ttk.Entry(
                date_frame,
                textvariable=self.date_var,
                font=self.font,
                width=20
            )

        self.date_entry.pack(side=tk.RIGHT, padx=(0, 10))

        # زر التاريخ الحالي
        today_btn = ttk.Button(
            date_frame,
            text="اليوم",
            command=self.set_today_date
        )
        today_btn.pack(side=tk.RIGHT)

        # الملاحظات
        notes_frame = ttk.Frame(form_frame)
        notes_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 15))

        tk.Label(notes_frame, text="الملاحظات:", font=self.font).pack(anchor=tk.E, pady=(0, 5))

        self.notes_text = tk.Text(
            notes_frame,
            height=4,
            font=self.font,
            wrap=tk.WORD
        )
        self.notes_text.pack(fill=tk.BOTH, expand=True)

        # أزرار التحكم
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill=tk.X, pady=(10, 0))

        # زر الحفظ
        save_btn = ttk.Button(
            button_frame,
            text="حفظ المصروف",
            command=self.save_expense,
            style="Accent.TButton"
        )
        save_btn.pack(side=tk.LEFT, padx=(0, 10))

        # زر المسح
        clear_btn = ttk.Button(
            button_frame,
            text="مسح النموذج",
            command=self.clear_form
        )
        clear_btn.pack(side=tk.LEFT)

        # زر الإغلاق
        close_btn = ttk.Button(
            button_frame,
            text="إغلاق",
            command=self.parent.quit
        )
        close_btn.pack(side=tk.RIGHT)

    def load_categories(self):
        """
        تحميل التصنيفات من قاعدة البيانات
        Load categories from database
        """
        try:
            categories = self.db.get_categories()
            self.category_combo['values'] = categories
            if categories:
                self.category_combo.set(categories[0])
        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ في تحميل التصنيفات: {str(e)}")

    def add_new_category(self):
        """
        إضافة تصنيف جديد
        Add new category
        """
        dialog = CategoryDialog(self.parent, self.db, self.font)
        self.parent.wait_window(dialog.dialog)  # انتظار إغلاق النافذة
        if dialog.result:
            self.load_categories()
            self.category_combo.set(dialog.result)

    def set_today_date(self):
        """
        تعيين التاريخ الحالي
        Set today's date
        """
        try:
            if hasattr(self.date_entry, 'set_date'):
                self.date_entry.set_date(datetime.now().date())
            else:
                self.date_var.set(datetime.now().strftime('%Y-%m-%d'))
        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ في تعيين التاريخ: {str(e)}")

    def validate_input(self):
        """
        التحقق من صحة البيانات المدخلة
        Validate input data
        """
        # التحقق من المبلغ
        try:
            amount = float(self.amount_var.get())
            if amount <= 0:
                messagebox.showerror("خطأ", "يجب أن يكون المبلغ أكبر من صفر")
                return False
        except ValueError:
            messagebox.showerror("خطأ", "يرجى إدخال مبلغ صحيح")
            return False

        # التحقق من التصنيف
        if not self.category_var.get():
            messagebox.showerror("خطأ", "يرجى اختيار تصنيف")
            return False

        # التحقق من التاريخ
        try:
            if hasattr(self.date_entry, 'get_date'):
                date_str = self.date_entry.get_date().strftime('%Y-%m-%d')
            else:
                date_str = self.date_var.get()
                datetime.strptime(date_str, '%Y-%m-%d')
        except ValueError:
            messagebox.showerror("خطأ", "يرجى إدخال تاريخ صحيح (YYYY-MM-DD)")
            return False

        return True

    def save_expense(self):
        """
        حفظ المصروف
        Save expense
        """
        if not self.validate_input():
            return

        try:
            amount = float(self.amount_var.get())
            category = self.category_var.get()

            if hasattr(self.date_entry, 'get_date'):
                date_str = self.date_entry.get_date().strftime('%Y-%m-%d')
            else:
                date_str = self.date_var.get()

            notes = self.notes_text.get("1.0", tk.END).strip()

            success = self.db.add_expense(amount, category, date_str, notes)

            if success:
                self.clear_form()
                if self.callback:
                    self.callback()
            else:
                messagebox.showerror("خطأ", "فشل في حفظ المصروف")

        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ أثناء الحفظ: {str(e)}")

    def clear_form(self):
        """
        مسح النموذج
        Clear form
        """
        self.amount_var.set("")
        if self.category_combo['values']:
            self.category_combo.set(self.category_combo['values'][0])
        self.set_today_date()
        self.notes_text.delete("1.0", tk.END)


class CategoryDialog:
    def __init__(self, parent, database, font):
        """
        نافذة حوار إضافة تصنيف جديد
        Dialog for adding new category
        """
        self.db = database
        self.font = font
        self.result = None

        # إنشاء النافذة
        self.dialog = tk.Toplevel(parent)
        self.dialog.title("إضافة تصنيف جديد")
        self.dialog.geometry("300x150")
        self.dialog.resizable(False, False)
        self.dialog.grab_set()

        # توسيط النافذة
        self.dialog.transient(parent)

        self.create_dialog()

    def create_dialog(self):
        """
        إنشاء محتوى النافذة
        Create dialog content
        """
        main_frame = ttk.Frame(self.dialog, padding=20)
        main_frame.pack(fill=tk.BOTH, expand=True)

        # اسم التصنيف
        tk.Label(main_frame, text="اسم التصنيف:", font=self.font).pack(anchor=tk.E, pady=(0, 5))

        self.name_var = tk.StringVar()
        name_entry = ttk.Entry(main_frame, textvariable=self.name_var, font=self.font, width=25)
        name_entry.pack(pady=(0, 15))
        name_entry.focus()

        # أزرار التحكم
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill=tk.X)

        ttk.Button(button_frame, text="إضافة", command=self.add_category).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(button_frame, text="إلغاء", command=self.dialog.destroy).pack(side=tk.LEFT)

        # ربط مفتاح Enter
        self.dialog.bind('<Return>', lambda e: self.add_category())

    def add_category(self):
        """
        إضافة التصنيف
        Add category
        """
        name = self.name_var.get().strip()

        if not name:
            messagebox.showerror("خطأ", "يرجى إدخال اسم التصنيف")
            return

        try:
            success = self.db.add_category(name)

            if success:
                self.result = name
                messagebox.showinfo("نجح", f"تم إضافة التصنيف '{name}' بنجاح!")
                self.dialog.destroy()
            else:
                messagebox.showerror("خطأ", "فشل في إضافة التصنيف أو أنه موجود مسبقاً")
        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ أثناء إضافة التصنيف: {str(e)}")
