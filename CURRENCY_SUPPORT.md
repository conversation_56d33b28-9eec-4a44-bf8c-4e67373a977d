# دعم العملات المتعددة - Multi-Currency Support

## 🌍 العملات المدعومة

### العملات المتاحة:
1. **🇸🇦 ريال سعودي (SAR)** - ر.س
2. **🇸🇾 ليرة سورية (SYP)** - ل.س  
3. **🇺🇸 دولار أمريكي (USD)** - $
4. **🇶🇦 ريال قطري (QAR)** - ر.ق

## 🔧 التحسينات المُطبقة

### 1. تحديث قاعدة البيانات

#### جدول المصاريف المحسن:
```sql
CREATE TABLE expenses (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    amount REAL NOT NULL,
    category TEXT NOT NULL,
    date TEXT NOT NULL,
    notes TEXT,
    currency TEXT DEFAULT 'SAR',  -- <PERSON><PERSON><PERSON><PERSON> العملة الجديد
    created_at TEXT DEFAULT CURRENT_TIMESTAMP
);
```

#### جدول العملات الجديد:
```sql
CREATE TABLE currencies (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    code TEXT UNIQUE NOT NULL,      -- كود العملة (SAR, USD, etc.)
    name TEXT NOT NULL,             -- اسم العملة
    symbol TEXT NOT NULL,           -- رمز العملة (ر.س, $, etc.)
    exchange_rate REAL DEFAULT 1.0  -- سعر الصرف مقابل الريال السعودي
);
```

### 2. أسعار الصرف الافتراضية

| العملة | الكود | الرمز | سعر الصرف (مقابل الريال السعودي) |
|--------|-------|-------|--------------------------------|
| ريال سعودي | SAR | ر.س | 1.00 (العملة الأساسية) |
| ليرة سورية | SYP | ل.س | 0.0004 (تقريبي) |
| دولار أمريكي | USD | $ | 3.75 (تقريبي) |
| ريال قطري | QAR | ر.ق | 1.03 (تقريبي) |

### 3. واجهة المستخدم المحسنة

#### حقل العملة الجديد:
```
💱 العملة:
┌─────────────────────────────────┐
│ [▼ ريال سعودي (ر.س)] [💱 تحديث] │
└─────────────────────────────────┘
```

#### تحديث حقل المبلغ:
```
💰 المبلغ:
┌─────────────────────────────────┐
│ [___________________] [ر.س]    │  ← يتغير حسب العملة المختارة
└─────────────────────────────────┘
```

#### جدول المصاريف المحسن:
| الرقم | المبلغ | العملة | التصنيف | التاريخ | الملاحظات |
|------|--------|--------|----------|---------|-----------|
| 1 | 50.00 | ر.س | طعام | 2024-01-15 | غداء |
| 2 | 10.00 | $ | مواصلات | 2024-01-14 | تاكسي |
| 3 | 5000.00 | ل.س | تسوق | 2024-01-13 | ملابس |

## 🔄 ميزات التحويل

### 1. تحويل العملات التلقائي
- **المجموع الكلي**: يتم تحويل جميع المبالغ إلى الريال السعودي
- **الإحصائيات**: تعرض بالريال السعودي للتوحيد
- **التقارير**: تدعم العملات المختلطة

### 2. دوال التحويل الجديدة

#### تحويل العملة:
```python
def convert_currency(amount, from_currency, to_currency="SAR"):
    # تحويل المبلغ من عملة إلى أخرى
    # المثال: تحويل 100 دولار إلى ريال سعودي
    sar_amount = convert_currency(100, "USD", "SAR")  # = 375 ريال
```

#### الحصول على رمز العملة:
```python
def get_currency_symbol(currency_code):
    # SAR → ر.س
    # USD → $
    # SYP → ل.س
    # QAR → ر.ق
```

## 🎯 كيفية الاستخدام

### إضافة مصروف بعملة مختلفة:

1. **انتقل إلى تبويب "إضافة مصروف"**
2. **أدخل المبلغ**: مثل 100
3. **اختر العملة**: من القائمة المنسدلة
   - ريال سعودي (ر.س)
   - ليرة سورية (ل.س)
   - دولار أمريكي ($)
   - ريال قطري (ر.ق)
4. **لاحظ تغيير رمز العملة**: في حقل المبلغ
5. **أكمل باقي البيانات** واضغط حفظ

### عرض المصاريف بعملات مختلفة:

1. **انتقل إلى تبويب "عرض المصاريف"**
2. **شاهد عمود العملة الجديد** في الجدول
3. **المجموع الكلي**: يظهر بالريال السعودي (محول تلقائياً)

### تعديل عملة مصروف موجود:

1. **انقر نقراً مزدوجاً** على المصروف في الجدول
2. **غير العملة** من القائمة المنسدلة
3. **احفظ التغييرات**

## 💡 أمثلة عملية

### مثال 1: مصاريف متنوعة
- **إفطار**: 25 ر.س (ريال سعودي)
- **تاكسي**: 5 $ (دولار أمريكي) = 18.75 ر.س
- **قهوة**: 2000 ل.س (ليرة سورية) = 0.80 ر.س
- **المجموع**: 44.55 ر.س

### مثال 2: سفر إلى قطر
- **طيران**: 800 ر.س
- **فندق**: 200 ر.ق = 206 ر.س
- **طعام**: 50 ر.ق = 51.50 ر.س
- **المجموع**: 1057.50 ر.س

## 🔧 الميزات التقنية

### 1. التوافق مع البيانات القديمة
- **ترقية تلقائية**: للمصاريف الموجودة
- **عملة افتراضية**: SAR للبيانات القديمة
- **عدم فقدان البيانات**: ضمان الحفاظ على جميع المصاريف

### 2. معالجة الأخطاء
- **التحقق من صحة العملة**: قبل الحفظ
- **قيم افتراضية**: في حالة عدم تحديد العملة
- **رسائل خطأ واضحة**: للمستخدم

### 3. الأداء المحسن
- **فهرسة العملات**: للبحث السريع
- **تخزين مؤقت**: لأسعار الصرف
- **استعلامات محسنة**: لقاعدة البيانات

## 🚀 الميزات المستقبلية

### المخطط لها:
1. **تحديث أسعار الصرف**: من API خارجي
2. **عملات إضافية**: يورو، جنيه إسترليني، درهم إماراتي
3. **تقارير بعملات مختلفة**: عرض الإحصائيات بأي عملة
4. **تنبيهات تغيير الأسعار**: عند تقلب أسعار الصرف
5. **محفظة متعددة العملات**: لكل عملة محفظة منفصلة

### التحسينات التقنية:
1. **API أسعار الصرف**: ربط مع خدمات مالية
2. **تحديث تلقائي**: للأسعار يومياً
3. **تاريخ أسعار الصرف**: لتتبع التغييرات
4. **تحليلات متقدمة**: تأثير تقلبات العملة

## ✅ الفوائد المحققة

### للمستخدمين:
- ✅ **مرونة كاملة**: في اختيار العملة
- ✅ **دقة في التتبع**: للمصاريف بعملات مختلفة
- ✅ **سهولة المقارنة**: بتحويل تلقائي للريال السعودي
- ✅ **واجهة بديهية**: لاختيار وتغيير العملة

### للتطبيق:
- ✅ **قاعدة بيانات محسنة**: مع دعم العملات
- ✅ **مرونة في التطوير**: لإضافة عملات جديدة
- ✅ **توافق عكسي**: مع البيانات القديمة
- ✅ **أداء محسن**: مع فهرسة ذكية

## 🎊 الخلاصة

**تم إضافة دعم شامل للعملات المتعددة!**

الآن يمكن للمستخدمين:
- **تسجيل المصاريف** بأي من العملات الأربع المدعومة
- **عرض وإدارة** المصاريف بعملات مختلطة
- **الحصول على إحصائيات دقيقة** مع التحويل التلقائي
- **تعديل العملة** لأي مصروف موجود

**التطبيق الآن جاهز للاستخدام الدولي!** 🌍
