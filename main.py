"""
تطبيق إدارة المصاريف الشخصية
Personal Expense Management Application
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
from datetime import datetime, timedelta
import csv
from database import ExpenseDatabase
from expense_form import ExpenseForm
from expense_table import ExpenseTable
from statistics_panel import StatisticsPanel

class ExpenseManagerApp:
    def __init__(self):
        """
        تهيئة التطبيق الرئيسي
        Initialize main application
        """
        self.root = tk.Tk()
        self.root.title("مدير المصاريف الشخصية - Personal Expense Manager")
        self.root.geometry("1200x800")
        self.root.minsize(800, 600)

        # تهيئة قاعدة البيانات
        self.db = ExpenseDatabase()

        # إعداد الخط العربي
        self.setup_fonts()

        # إنشاء الواجهة
        self.create_widgets()

        # تحديث البيانات
        self.refresh_data()

    def setup_fonts(self):
        """
        إعداد الخطوط للنصوص العربية
        Setup fonts for Arabic text
        """
        # خطوط محسنة للنصوص العربية
        self.arabic_font = ("Segoe UI", 11)
        self.arabic_font_bold = ("Segoe UI", 11, "bold")
        self.arabic_font_large = ("Segoe UI", 16, "bold")
        self.arabic_font_medium = ("Segoe UI", 13, "bold")
        self.arabic_font_small = ("Segoe UI", 9)

        # ألوان احترافية
        self.colors = {
            'primary': '#2c3e50',      # أزرق داكن
            'secondary': '#3498db',    # أزرق فاتح
            'success': '#27ae60',      # أخضر
            'warning': '#f39c12',      # برتقالي
            'danger': '#e74c3c',       # أحمر
            'info': '#17a2b8',         # أزرق فاتح
            'light': '#f8f9fa',        # رمادي فاتح جداً
            'dark': '#343a40',         # رمادي داكن
            'white': '#ffffff',        # أبيض
            'background': '#ecf0f1',   # خلفية رمادية فاتحة
            'card': '#ffffff',         # خلفية البطاقات
            'border': '#dee2e6'        # حدود
        }

    def setup_styles(self):
        """
        إعداد الستايلات الاحترافية
        Setup professional styles
        """
        style = ttk.Style()

        # ستايل الأزرار الرئيسية
        style.configure(
            "Primary.TButton",
            font=self.arabic_font_bold,
            padding=(20, 10),
            relief="flat"
        )

        # ستايل الأزرار الثانوية
        style.configure(
            "Secondary.TButton",
            font=self.arabic_font,
            padding=(15, 8),
            relief="flat"
        )

        # ستايل الأزرار الخطرة
        style.configure(
            "Danger.TButton",
            font=self.arabic_font,
            padding=(15, 8),
            relief="flat"
        )

        # ستايل النوافذ المبوبة
        style.configure(
            "Custom.TNotebook",
            tabposition='n',
            padding=5
        )

        style.configure(
            "Custom.TNotebook.Tab",
            font=self.arabic_font_bold,
            padding=(20, 12),
            relief="flat"
        )

        # ستايل الإطارات
        style.configure(
            "Card.TFrame",
            relief="flat",
            borderwidth=1,
            background=self.colors['card']
        )

    def create_header(self, parent):
        """
        إنشاء شريط العنوان الاحترافي
        Create professional header
        """
        # إطار الهيدر
        header_frame = tk.Frame(parent, bg=self.colors['primary'], height=80)
        header_frame.pack(fill=tk.X, pady=(0, 20))
        header_frame.pack_propagate(False)

        # العنوان الرئيسي
        title_label = tk.Label(
            header_frame,
            text="💰 مدير المصاريف الشخصية",
            font=self.arabic_font_large,
            fg=self.colors['white'],
            bg=self.colors['primary']
        )
        title_label.pack(expand=True)

        # العنوان الفرعي
        subtitle_label = tk.Label(
            header_frame,
            text="إدارة ذكية لمصاريفك اليومية",
            font=self.arabic_font,
            fg=self.colors['light'],
            bg=self.colors['primary']
        )
        subtitle_label.pack()

    def create_widgets(self):
        """
        إنشاء عناصر الواجهة
        Create UI widgets
        """
        # تطبيق ستايل احترافي
        self.setup_styles()

        # تعيين خلفية النافذة
        self.root.configure(bg=self.colors['background'])

        # الإطار الرئيسي
        main_frame = tk.Frame(self.root, bg=self.colors['background'])
        main_frame.pack(fill=tk.BOTH, expand=True, padx=15, pady=15)

        # إنشاء شريط العنوان
        self.create_header(main_frame)

        # إطار الأزرار العلوية
        button_frame = tk.Frame(main_frame, bg=self.colors['background'])
        button_frame.pack(fill=tk.X, pady=(0, 15))

        # أزرار التحكم
        self.create_control_buttons(button_frame)

        # إطار المحتوى الرئيسي
        content_frame = tk.Frame(main_frame, bg=self.colors['background'])
        content_frame.pack(fill=tk.BOTH, expand=True)

        # إنشاء النوافذ المبوبة
        self.notebook = ttk.Notebook(content_frame, style="Custom.TNotebook")
        self.notebook.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # تبويب إدخال المصاريف
        self.expense_form_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.expense_form_frame, text="إضافة مصروف")

        # تبويب عرض المصاريف
        self.expense_table_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.expense_table_frame, text="عرض المصاريف")

        # تبويب الإحصائيات
        self.statistics_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.statistics_frame, text="الإحصائيات")

        # إنشاء المكونات
        self.create_components()

    def create_control_buttons(self, parent):
        """
        إنشاء أزرار التحكم الاحترافية
        Create professional control buttons
        """
        # إطار الأزرار اليسرى
        left_buttons = tk.Frame(parent, bg=self.colors['background'])
        left_buttons.pack(side=tk.LEFT)

        # زر تحديث البيانات
        refresh_btn = ttk.Button(
            left_buttons,
            text="🔄 تحديث البيانات",
            command=self.refresh_data,
            style="Secondary.TButton"
        )
        refresh_btn.pack(side=tk.LEFT, padx=(0, 10))

        # زر تصدير البيانات
        export_btn = ttk.Button(
            left_buttons,
            text="📊 تصدير إلى CSV",
            command=self.export_to_csv,
            style="Secondary.TButton"
        )
        export_btn.pack(side=tk.LEFT, padx=(0, 10))

        # زر نسخة احتياطية
        backup_btn = ttk.Button(
            left_buttons,
            text="💾 نسخة احتياطية",
            command=self.create_backup,
            style="Secondary.TButton"
        )
        backup_btn.pack(side=tk.LEFT, padx=(0, 10))

        # إطار الأزرار اليمنى
        right_buttons = tk.Frame(parent, bg=self.colors['background'])
        right_buttons.pack(side=tk.RIGHT)

        # زر حول التطبيق
        about_btn = ttk.Button(
            right_buttons,
            text="ℹ️ حول التطبيق",
            command=self.show_about,
            style="Secondary.TButton"
        )
        about_btn.pack(side=tk.RIGHT)

    def create_components(self):
        """
        إنشاء المكونات الفرعية
        Create sub-components
        """
        # تمرير الخطوط والألوان للمكونات
        fonts = {
            'default': self.arabic_font,
            'bold': self.arabic_font_bold,
            'large': self.arabic_font_large,
            'medium': self.arabic_font_medium,
            'small': self.arabic_font_small
        }

        # نموذج إدخال المصاريف
        self.expense_form = ExpenseForm(
            self.expense_form_frame,
            self.db,
            fonts,
            self.colors,
            self.on_expense_added
        )

        # جدول المصاريف
        self.expense_table = ExpenseTable(
            self.expense_table_frame,
            self.db,
            fonts,
            self.colors,
            self.on_expense_updated
        )

        # لوحة الإحصائيات
        self.statistics_panel = StatisticsPanel(
            self.statistics_frame,
            self.db,
            fonts,
            self.colors
        )

    def refresh_data(self):
        """
        تحديث جميع البيانات
        Refresh all data
        """
        try:
            self.expense_table.refresh()
            self.statistics_panel.refresh()
        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ أثناء تحديث البيانات: {str(e)}")

    def on_expense_added(self):
        """
        معالج إضافة مصروف جديد
        Handler for new expense added
        """
        self.refresh_data()
        messagebox.showinfo("نجح", "تم إضافة المصروف بنجاح!")

    def on_expense_updated(self):
        """
        معالج تحديث المصاريف
        Handler for expense updates
        """
        self.refresh_data()

    def export_to_csv(self):
        """
        تصدير البيانات إلى ملف CSV
        Export data to CSV file
        """
        try:
            file_path = filedialog.asksaveasfilename(
                defaultextension=".csv",
                filetypes=[("CSV files", "*.csv"), ("All files", "*.*")],
                title="حفظ ملف CSV"
            )

            if file_path:
                expenses = self.db.get_all_expenses()

                with open(file_path, 'w', newline='', encoding='utf-8-sig') as csvfile:
                    writer = csv.writer(csvfile)

                    # كتابة العناوين
                    writer.writerow(['الرقم', 'المبلغ', 'التصنيف', 'التاريخ', 'الملاحظات', 'تاريخ الإنشاء'])

                    # كتابة البيانات
                    for expense in expenses:
                        writer.writerow(expense)

                messagebox.showinfo("نجح", f"تم تصدير البيانات بنجاح إلى:\n{file_path}")

        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ أثناء التصدير: {str(e)}")

    def create_backup(self):
        """
        إنشاء نسخة احتياطية من قاعدة البيانات
        Create database backup
        """
        try:
            import shutil
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_path = f"backup_expenses_{timestamp}.db"

            shutil.copy2(self.db.db_path, backup_path)
            messagebox.showinfo("نجح", f"تم إنشاء النسخة الاحتياطية:\n{backup_path}")

        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ أثناء إنشاء النسخة الاحتياطية: {str(e)}")

    def show_about(self):
        """
        عرض معلومات حول التطبيق
        Show about dialog
        """
        about_text = """
        مدير المصاريف الشخصية
        Personal Expense Manager

        الإصدار: 1.0
        Version: 1.0

        تطبيق بسيط لإدارة المصاريف الشخصية
        A simple application for managing personal expenses

        المطور: مساعد الذكي
        Developer: AI Assistant
        """

        messagebox.showinfo("حول التطبيق", about_text)

    def run(self):
        """
        تشغيل التطبيق
        Run the application
        """
        self.root.mainloop()

if __name__ == "__main__":
    app = ExpenseManagerApp()
    app.run()
