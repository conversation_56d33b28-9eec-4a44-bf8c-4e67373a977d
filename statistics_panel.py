"""
لوحة الإحصائيات والتقارير
Statistics and reports panel
"""

import tkinter as tk
from tkinter import ttk
from datetime import datetime, timedelta
import matplotlib.pyplot as plt
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
import matplotlib.dates as mdates

class StatisticsPanel:
    def __init__(self, parent, database, fonts, colors):
        """
        تهيئة لوحة الإحصائيات المحسنة
        Initialize enhanced statistics panel
        """
        self.parent = parent
        self.db = database
        self.fonts = fonts
        self.colors = colors

        # للتوافق مع الكود القديم
        self.font = fonts['default']

        self.create_panel()
        self.refresh()

    def create_panel(self):
        """
        إنشاء لوحة الإحصائيات المحسنة
        Create enhanced statistics panel
        """
        # تعيين خلفية الإطار الرئيسي (إذا كان Frame عادي)
        try:
            self.parent.configure(bg=self.colors['background'])
        except:
            pass  # في حالة كان ttk.Frame

        # الإطار الرئيسي
        main_frame = tk.Frame(self.parent, bg=self.colors['background'])
        main_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)

        # العنوان مع أيقونة
        title_frame = tk.Frame(main_frame, bg=self.colors['background'])
        title_frame.pack(fill=tk.X, pady=(0, 30))

        title_label = tk.Label(
            title_frame,
            text="📈 الإحصائيات والتقارير",
            font=self.fonts['large'],
            fg=self.colors['primary'],
            bg=self.colors['background']
        )
        title_label.pack()

        subtitle_label = tk.Label(
            title_frame,
            text="تحليل شامل لمصاريفك وعاداتك المالية",
            font=self.fonts['default'],
            fg=self.colors['dark'],
            bg=self.colors['background']
        )
        subtitle_label.pack(pady=(5, 0))

        # إطار الإحصائيات السريعة
        quick_stats_frame = ttk.LabelFrame(main_frame, text="إحصائيات سريعة", padding=15)
        quick_stats_frame.pack(fill=tk.X, pady=(0, 15))

        # إنشاء شبكة للإحصائيات
        stats_grid = ttk.Frame(quick_stats_frame)
        stats_grid.pack(fill=tk.X)

        # إحصائيات اليوم
        today_frame = ttk.Frame(stats_grid)
        today_frame.grid(row=0, column=0, padx=10, pady=5, sticky="ew")

        tk.Label(today_frame, text="مصاريف اليوم:", font=self.font).pack(anchor=tk.E)
        self.today_label = tk.Label(today_frame, text="0.00 ريال", font=(self.font[0], 12, "bold"), fg="#e74c3c")
        self.today_label.pack(anchor=tk.E)

        # إحصائيات الأسبوع
        week_frame = ttk.Frame(stats_grid)
        week_frame.grid(row=0, column=1, padx=10, pady=5, sticky="ew")

        tk.Label(week_frame, text="مصاريف الأسبوع:", font=self.font).pack(anchor=tk.E)
        self.week_label = tk.Label(week_frame, text="0.00 ريال", font=(self.font[0], 12, "bold"), fg="#f39c12")
        self.week_label.pack(anchor=tk.E)

        # إحصائيات الشهر
        month_frame = ttk.Frame(stats_grid)
        month_frame.grid(row=0, column=2, padx=10, pady=5, sticky="ew")

        tk.Label(month_frame, text="مصاريف الشهر:", font=self.font).pack(anchor=tk.E)
        self.month_label = tk.Label(month_frame, text="0.00 ريال", font=(self.font[0], 12, "bold"), fg="#9b59b6")
        self.month_label.pack(anchor=tk.E)

        # إحصائيات العام
        year_frame = ttk.Frame(stats_grid)
        year_frame.grid(row=1, column=0, padx=10, pady=5, sticky="ew")

        tk.Label(year_frame, text="مصاريف العام:", font=self.font).pack(anchor=tk.E)
        self.year_label = tk.Label(year_frame, text="0.00 ريال", font=(self.font[0], 12, "bold"), fg="#2ecc71")
        self.year_label.pack(anchor=tk.E)

        # إجمالي المصاريف
        total_frame = ttk.Frame(stats_grid)
        total_frame.grid(row=1, column=1, padx=10, pady=5, sticky="ew")

        tk.Label(total_frame, text="إجمالي المصاريف:", font=self.font).pack(anchor=tk.E)
        self.total_label = tk.Label(total_frame, text="0.00 ريال", font=(self.font[0], 12, "bold"), fg="#34495e")
        self.total_label.pack(anchor=tk.E)

        # عدد المصاريف
        count_frame = ttk.Frame(stats_grid)
        count_frame.grid(row=1, column=2, padx=10, pady=5, sticky="ew")

        tk.Label(count_frame, text="عدد المصاريف:", font=self.font).pack(anchor=tk.E)
        self.count_label = tk.Label(count_frame, text="0", font=(self.font[0], 12, "bold"), fg="#95a5a6")
        self.count_label.pack(anchor=tk.E)

        # تكوين الشبكة
        for i in range(3):
            stats_grid.columnconfigure(i, weight=1)

        # إطار التصنيفات
        categories_frame = ttk.LabelFrame(main_frame, text="المصاريف حسب التصنيف", padding=15)
        categories_frame.pack(fill=tk.X, pady=(0, 15))

        # جدول التصنيفات
        self.create_categories_table(categories_frame)

        # إطار الرسوم البيانية
        charts_frame = ttk.LabelFrame(main_frame, text="الرسوم البيانية", padding=15)
        charts_frame.pack(fill=tk.BOTH, expand=True)

        # أزرار الرسوم البيانية
        chart_buttons_frame = ttk.Frame(charts_frame)
        chart_buttons_frame.pack(fill=tk.X, pady=(0, 10))

        ttk.Button(
            chart_buttons_frame,
            text="رسم بياني للتصنيفات",
            command=self.show_category_chart
        ).pack(side=tk.LEFT, padx=(0, 10))

        ttk.Button(
            chart_buttons_frame,
            text="رسم بياني زمني",
            command=self.show_time_chart
        ).pack(side=tk.LEFT, padx=(0, 10))

        ttk.Button(
            chart_buttons_frame,
            text="تحديث الإحصائيات",
            command=self.refresh
        ).pack(side=tk.RIGHT)

        # إطار الرسم البياني
        self.chart_frame = ttk.Frame(charts_frame)
        self.chart_frame.pack(fill=tk.BOTH, expand=True)

    def create_categories_table(self, parent):
        """
        إنشاء جدول التصنيفات
        Create categories table
        """
        # إطار الجدول
        table_frame = ttk.Frame(parent)
        table_frame.pack(fill=tk.BOTH, expand=True)

        # الجدول
        columns = ('category', 'total', 'percentage')
        self.categories_tree = ttk.Treeview(table_frame, columns=columns, show='headings', height=6)

        # تعريف العناوين
        self.categories_tree.heading('category', text='التصنيف')
        self.categories_tree.heading('total', text='المجموع')
        self.categories_tree.heading('percentage', text='النسبة المئوية')

        # تعريف عرض الأعمدة
        self.categories_tree.column('category', width=150, anchor=tk.CENTER)
        self.categories_tree.column('total', width=100, anchor=tk.CENTER)
        self.categories_tree.column('percentage', width=100, anchor=tk.CENTER)

        # شريط التمرير
        categories_scrollbar = ttk.Scrollbar(table_frame, orient=tk.VERTICAL, command=self.categories_tree.yview)
        self.categories_tree.configure(yscrollcommand=categories_scrollbar.set)

        # تخطيط الجدول
        self.categories_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        categories_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

    def refresh(self):
        """
        تحديث جميع الإحصائيات
        Refresh all statistics
        """
        try:
            self.update_quick_stats()
            self.update_categories_stats()
        except Exception as e:
            print(f"خطأ في تحديث الإحصائيات: {e}")

    def update_quick_stats(self):
        """
        تحديث الإحصائيات السريعة
        Update quick statistics
        """
        today = datetime.now().date()
        week_start = today - timedelta(days=today.weekday())
        month_start = today.replace(day=1)
        year_start = today.replace(month=1, day=1)

        # مصاريف اليوم
        today_expenses = self.db.get_expenses_by_date_range(
            today.strftime('%Y-%m-%d'),
            today.strftime('%Y-%m-%d')
        )
        today_total = sum(exp[1] for exp in today_expenses)
        self.today_label.config(text=f"{today_total:.2f} ريال")

        # مصاريف الأسبوع
        week_expenses = self.db.get_expenses_by_date_range(
            week_start.strftime('%Y-%m-%d'),
            today.strftime('%Y-%m-%d')
        )
        week_total = sum(exp[1] for exp in week_expenses)
        self.week_label.config(text=f"{week_total:.2f} ريال")

        # مصاريف الشهر
        month_expenses = self.db.get_expenses_by_date_range(
            month_start.strftime('%Y-%m-%d'),
            today.strftime('%Y-%m-%d')
        )
        month_total = sum(exp[1] for exp in month_expenses)
        self.month_label.config(text=f"{month_total:.2f} ريال")

        # مصاريف العام
        year_expenses = self.db.get_expenses_by_date_range(
            year_start.strftime('%Y-%m-%d'),
            today.strftime('%Y-%m-%d')
        )
        year_total = sum(exp[1] for exp in year_expenses)
        self.year_label.config(text=f"{year_total:.2f} ريال")

        # إجمالي المصاريف
        all_expenses = self.db.get_all_expenses()
        total_amount = sum(exp[1] for exp in all_expenses)
        self.total_label.config(text=f"{total_amount:.2f} ريال")

        # عدد المصاريف
        self.count_label.config(text=str(len(all_expenses)))

    def update_categories_stats(self):
        """
        تحديث إحصائيات التصنيفات
        Update categories statistics
        """
        # مسح البيانات الحالية
        for item in self.categories_tree.get_children():
            self.categories_tree.delete(item)

        # جلب البيانات
        category_totals = self.db.get_total_by_category()

        if not category_totals:
            return

        # حساب المجموع الكلي
        grand_total = sum(total[1] for total in category_totals)

        # إدراج البيانات
        for category, total in category_totals:
            percentage = (total / grand_total * 100) if grand_total > 0 else 0
            self.categories_tree.insert('', tk.END, values=(
                category,
                f"{total:.2f} ريال",
                f"{percentage:.1f}%"
            ))

    def show_category_chart(self):
        """
        عرض الرسم البياني للتصنيفات
        Show category chart
        """
        try:
            # مسح الرسم البياني السابق
            for widget in self.chart_frame.winfo_children():
                widget.destroy()

            # جلب البيانات
            category_totals = self.db.get_total_by_category()

            if not category_totals:
                tk.Label(self.chart_frame, text="لا توجد بيانات لعرضها", font=self.font).pack(expand=True)
                return

            # إعداد الرسم البياني
            fig, ax = plt.subplots(figsize=(8, 6))

            categories = [item[0] for item in category_totals[:10]]  # أول 10 تصنيفات
            amounts = [item[1] for item in category_totals[:10]]

            # إنشاء الرسم البياني الدائري
            colors = plt.cm.Set3(range(len(categories)))
            wedges, texts, autotexts = ax.pie(amounts, labels=categories, autopct='%1.1f%%', colors=colors)

            # تحسين النص
            for autotext in autotexts:
                autotext.set_color('white')
                autotext.set_fontweight('bold')

            ax.set_title('توزيع المصاريف حسب التصنيف', fontsize=14, fontweight='bold')

            # إضافة الرسم البياني إلى الواجهة
            canvas = FigureCanvasTkAgg(fig, self.chart_frame)
            canvas.draw()
            canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True)

        except Exception as e:
            tk.Label(self.chart_frame, text=f"خطأ في عرض الرسم البياني: {str(e)}", font=self.font).pack(expand=True)

    def show_time_chart(self):
        """
        عرض الرسم البياني الزمني
        Show time chart
        """
        try:
            # مسح الرسم البياني السابق
            for widget in self.chart_frame.winfo_children():
                widget.destroy()

            # جلب البيانات
            expenses = self.db.get_all_expenses()

            if not expenses:
                tk.Label(self.chart_frame, text="لا توجد بيانات لعرضها", font=self.font).pack(expand=True)
                return

            # تجميع البيانات حسب التاريخ
            daily_totals = {}
            for expense in expenses:
                date = expense[3]  # التاريخ
                amount = expense[1]  # المبلغ

                if date in daily_totals:
                    daily_totals[date] += amount
                else:
                    daily_totals[date] = amount

            # ترتيب البيانات حسب التاريخ
            sorted_dates = sorted(daily_totals.keys())
            dates = [datetime.strptime(date, '%Y-%m-%d') for date in sorted_dates]
            amounts = [daily_totals[date] for date in sorted_dates]

            # إعداد الرسم البياني
            fig, ax = plt.subplots(figsize=(10, 6))

            ax.plot(dates, amounts, marker='o', linewidth=2, markersize=6, color='#3498db')
            ax.fill_between(dates, amounts, alpha=0.3, color='#3498db')

            # تنسيق المحاور
            ax.set_xlabel('التاريخ', fontsize=12)
            ax.set_ylabel('المبلغ (ريال)', fontsize=12)
            ax.set_title('تطور المصاريف عبر الزمن', fontsize=14, fontweight='bold')

            # تنسيق التواريخ
            ax.xaxis.set_major_formatter(mdates.DateFormatter('%Y-%m-%d'))
            ax.xaxis.set_major_locator(mdates.DayLocator(interval=max(1, len(dates)//10)))
            plt.xticks(rotation=45)

            # تحسين التخطيط
            plt.tight_layout()

            # إضافة الرسم البياني إلى الواجهة
            canvas = FigureCanvasTkAgg(fig, self.chart_frame)
            canvas.draw()
            canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True)

        except Exception as e:
            tk.Label(self.chart_frame, text=f"خطأ في عرض الرسم البياني: {str(e)}", font=self.font).pack(expand=True)
