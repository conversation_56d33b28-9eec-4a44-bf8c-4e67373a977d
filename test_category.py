"""
اختبار إضافة التصنيفات
Test adding categories
"""

from database import ExpenseDatabase

def test_add_category():
    """اختبار إضافة تصنيف جديد"""
    db = ExpenseDatabase()
    
    print("التصنيفات الحالية:")
    categories = db.get_categories()
    for i, cat in enumerate(categories, 1):
        print(f"{i}. {cat}")
    
    print("\nإضافة تصنيف جديد...")
    
    # اختبار إضافة تصنيف جديد
    new_category = "اختبار تصنيف"
    result = db.add_category(new_category)
    
    if result:
        print(f"✅ تم إضافة التصنيف '{new_category}' بنجاح!")
    else:
        print(f"❌ فشل في إضافة التصنيف '{new_category}'")
    
    print("\nالتصنيفات بعد الإضافة:")
    categories = db.get_categories()
    for i, cat in enumerate(categories, 1):
        print(f"{i}. {cat}")
    
    # اختبار إضافة نفس التصنيف مرة أخرى
    print(f"\nمحاولة إضافة نفس التصنيف '{new_category}' مرة أخرى...")
    result2 = db.add_category(new_category)
    
    if result2:
        print("✅ تم إضافة التصنيف")
    else:
        print("❌ لم يتم إضافة التصنيف (متوقع - التصنيف موجود مسبقاً)")

if __name__ == "__main__":
    test_add_category()
